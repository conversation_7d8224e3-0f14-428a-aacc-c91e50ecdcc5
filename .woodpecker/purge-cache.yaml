---
variables:
  - &minio_image 'minio/mc:RELEASE.2021-10-07T04-19-58Z'
  - &minio_environment
    AWS_ACCESS_KEY_ID:
      from_secret: cache_s3_access_key
    AWS_SECRET_ACCESS_KEY:
      from_secret: cache_s3_secret_key
    CACHE_BUCKET:
      from_secret: cache_s3_bucket
    MC_HOST:
      from_secret: cache_s3_server

when:
  - event: [ push , manual ]
    branch: ${CI_REPO_DEFAULT_BRANCH}
  - event: [ pull_request ]

skip_clone: true
steps:
  purge-opencloud-build:
    image: *minio_image
    commands:
      - mc alias set s3 $MC_HOST $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY
      - to_delete=$(mc find s3/$CACHE_BUCKET/desktop-build/ --older-than 1d)
      - if [ "$to_delete" = "" ]; then exit 0; fi
      - mc rm $to_delete
    environment:
      <<: *minio_environment

