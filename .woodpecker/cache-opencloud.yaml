---
variables:
  - &minio_image 'minio/mc:RELEASE.2021-10-07T04-19-58Z'
  - &minio_environment
    AWS_ACCESS_KEY_ID:
      from_secret: cache_s3_access_key
    AWS_SECRET_ACCESS_KEY:
      from_secret: cache_s3_secret_key
    CACHE_BUCKET:
      from_secret: cache_s3_bucket
    MC_HOST:
      from_secret: cache_s3_server

skip_clone: true
steps:
  - commands:
      - curl -o .woodpecker.env https://raw.githubusercontent.com/opencloud-eu/desktop/$CI_COMMIT_SHA/.woodpecker.env
      - curl -o script.sh https://raw.githubusercontent.com/opencloud-eu/desktop/$CI_COMMIT_SHA/test/gui/woodpecker/script.sh
      - . ./.woodpecker.env
      - mc alias set s3 $MC_HOST $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY
      - mc ls --recursive s3/$CACHE_BUCKET/opencloud-build
      - bash script.sh check_opencloud_cache
    environment:
      <<: *minio_environment
    image: *minio_image
    name: check-for-existing-cache
  - commands:
      - . ./.woodpecker.env
      - if $OPENCLOUD_CACHE_FOUND; then exit 0; fi
      - git clone -b $OPENCLOUD_BRANCH --single-branch https://github.com/opencloud-eu/opencloud.git repo_opencloud
      - cd repo_opencloud
      - git checkout $OPENCLOUD_COMMITID
    image: docker.io/golang:1.24
    name: clone-opencloud
  - commands:
      - . ./.woodpecker.env
      - if $OPENCLOUD_CACHE_FOUND; then exit 0; fi
      - cd repo_opencloud
      - retry -t 3 'make node-generate-prod'
    image: owncloudci/nodejs:20
    name: generate-opencloud
  - commands:
      - . ./.woodpecker.env
      - if $OPENCLOUD_CACHE_FOUND; then exit 0; fi
      - cd repo_opencloud
      - for i in $(seq 3); do make -C opencloud build && break || sleep 1; done
    image: docker.io/golang:1.24
    name: build-opencloud
  - commands:
      - . ./.woodpecker.env
      - if $OPENCLOUD_CACHE_FOUND; then exit 0; fi
      - mc alias set s3 $MC_HOST $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY
      - mc cp -a repo_opencloud/opencloud/bin/opencloud s3/$CACHE_BUCKET/opencloud-build/$OPENCLOUD_COMMITID/
      - mc ls --recursive s3/$CACHE_BUCKET/opencloud-build
    environment:
      <<: *minio_environment
    image: *minio_image
    name: upload-opencloud-cache
when:
  - branch:
      - main
      - stable-*
    event:
      - push
      - manual
  - event: pull_request
  - event: tag
