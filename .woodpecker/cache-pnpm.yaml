---
variables:
  - &minio_image 'minio/mc:RELEASE.2021-10-07T04-19-58Z'
  - &minio_environment
    AWS_ACCESS_KEY_ID:
      from_secret: cache_s3_access_key
    AWS_SECRET_ACCESS_KEY:
      from_secret: cache_s3_secret_key
    CACHE_BUCKET:
      from_secret: cache_s3_bucket
    MC_HOST:
      from_secret: cache_s3_server

steps:
  - commands:
      - mc alias set s3 $MC_HOST $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY
      - mc ls --recursive s3/$CACHE_BUCKET/web
      - bash test/gui/woodpecker/script.sh check_browsers_cache
    environment:
      <<: *minio_environment
    image: *minio_image
    name: check-browsers-cache
  - commands:
      - . ./.woodpecker.env
      - if $BROWSER_CACHE_FOUND; then exit 0; fi
      - cd test/gui/
      - npm i -s -g -f "$(jq -r ".packageManager" < webUI/package.json)"
      - pnpm config set store-dir ./.pnpm-store
      - make pnpm-install
    image: owncloudci/nodejs:20
    name: pnpm-install
  - commands:
      - . ./.woodpecker.env
      - if $BROWSER_CACHE_FOUND; then exit 0; fi
      - cd test/gui/
      - make pnpm-install-chromium
      - cd webUI
      - tar -czvf /woodpecker/desktop/playwright-browsers.tar.gz .playwright
    environment:
      PLAYWRIGHT_BROWSERS_PATH: .playwright
    image: owncloudci/nodejs:20
    name: install-browsers
  - commands:
      - . ./.woodpecker.env
      - if $BROWSER_CACHE_FOUND; then exit 0; fi
      - playwright_version=$(bash test/gui/woodpecker/script.sh get_playwright_version)
      - mc alias set s3 $MC_HOST $AWS_ACCESS_KEY_ID $AWS_SECRET_ACCESS_KEY
      - mc cp -r -a /woodpecker/desktop/playwright-browsers.tar.gz s3/$CACHE_BUCKET/web/browsers-cache/$playwright_version/
      - mc ls --recursive s3/$CACHE_BUCKET/web
    environment:
      <<: *minio_environment
    image: *minio_image
    name: upload-browsers-cache
when:
  - branch:
      - main
      - stable-*
    event:
      - push
      - manual
  - event: tag
  - event: pull_request
workspace:
  base: /woodpecker/
  path: desktop
