<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PACKAGES</key>
	<array>
		<dict>
			<key>MUST-CLOSE-APPLICATION-ITEMS</key>
			<array/>
			<key>MUST-CLOSE-APPLICATIONS</key>
			<false/>
			<key>PACKAGE_FILES</key>
			<dict>
				<key>DEFAULT_INSTALL_LOCATION</key>
				<string>/</string>
				<key>HIERARCHY</key>
				<dict>
					<key>CHILDREN</key>
					<array>
						<dict>
							<key>CHILDREN</key>
							<array>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>PATH</key>
									<string>@APPLICATION_SHORTNAME@.app</string>
									<key>PATH_TYPE</key>
									<integer>3</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>3</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>PATH</key>
									<string>Utilities</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>80</integer>
							<key>PATH</key>
							<string>Applications</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
							<key>PERMISSIONS</key>
							<integer>509</integer>
							<key>TYPE</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>CHILDREN</key>
							<array>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>PATH</key>
									<string>Application Support</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Documentation</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Filesystems</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Frameworks</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Input Methods</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Internet Plug-Ins</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>LaunchAgents</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>LaunchDaemons</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>PreferencePanes</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Preferences</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>PATH</key>
									<string>Printers</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>PrivilegedHelperTools</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>1005</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>QuickLook</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>QuickTime</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Screen Savers</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Scripts</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Services</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Widgets</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Automator</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Extensions</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>0</integer>
							<key>PATH</key>
							<string>Library</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
							<key>PERMISSIONS</key>
							<integer>493</integer>
							<key>TYPE</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>CHILDREN</key>
							<array>
								<dict>
									<key>CHILDREN</key>
									<array>
										<dict>
											<key>CHILDREN</key>
											<array/>
											<key>GID</key>
											<integer>0</integer>
											<key>PATH</key>
											<string>Extensions</string>
											<key>PATH_TYPE</key>
											<integer>0</integer>
											<key>PERMISSIONS</key>
											<integer>493</integer>
											<key>TYPE</key>
											<integer>1</integer>
											<key>UID</key>
											<integer>0</integer>
										</dict>
									</array>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Library</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>493</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>0</integer>
							<key>PATH</key>
							<string>System</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
							<key>PERMISSIONS</key>
							<integer>493</integer>
							<key>TYPE</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>CHILDREN</key>
							<array>
								<dict>
									<key>CHILDREN</key>
									<array/>
									<key>GID</key>
									<integer>0</integer>
									<key>PATH</key>
									<string>Shared</string>
									<key>PATH_TYPE</key>
									<integer>0</integer>
									<key>PERMISSIONS</key>
									<integer>1023</integer>
									<key>TYPE</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>80</integer>
							<key>PATH</key>
							<string>Users</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
							<key>PERMISSIONS</key>
							<integer>493</integer>
							<key>TYPE</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>GID</key>
					<integer>0</integer>
					<key>PATH</key>
					<string>/</string>
					<key>PATH_TYPE</key>
					<integer>0</integer>
					<key>PERMISSIONS</key>
					<integer>493</integer>
					<key>TYPE</key>
					<integer>1</integer>
					<key>UID</key>
					<integer>0</integer>
				</dict>
				<key>PAYLOAD_TYPE</key>
				<integer>0</integer>
				<key>PRESERVE_EXTENDED_ATTRIBUTES</key>
				<false/>
				<key>SHOW_INVISIBLE</key>
				<false/>
				<key>SPLIT_FORKS</key>
				<true/>
				<key>TREAT_MISSING_FILES_AS_WARNING</key>
				<false/>
				<key>VERSION</key>
				<integer>5</integer>
			</dict>
			<key>PACKAGE_SCRIPTS</key>
			<dict>
				<key>POSTINSTALL_PATH</key>
				<dict>
					<key>PATH</key>
					<string>@CMAKE_CURRENT_BINARY_DIR@/post_install.sh</string>
					<key>PATH_TYPE</key>
					<integer>0</integer>
				</dict>
				<key>PREINSTALL_PATH</key>
				<dict>
					<key>PATH</key>
					<string>@CMAKE_CURRENT_BINARY_DIR@/pre_install.sh</string>
					<key>PATH_TYPE</key>
					<integer>0</integer>
				</dict>
				<key>RESOURCES</key>
				<array/>
			</dict>
			<key>PACKAGE_SETTINGS</key>
			<dict>
				<key>AUTHENTICATION</key>
				<integer>1</integer>
				<key>CONCLUSION_ACTION</key>
				<integer>0</integer>
				<key>FOLLOW_SYMBOLIC_LINKS</key>
				<false/>
				<key>IDENTIFIER</key>
				<string>@APPLICATION_REV_DOMAIN@</string>
				<key>LOCATION</key>
				<integer>0</integer>
				<key>NAME</key>
				<string>@APPLICATION_NAME@</string>
				<key>OVERWRITE_PERMISSIONS</key>
				<false/>
				<key>PAYLOAD_SIZE</key>
				<integer>-1</integer>
				<key>REFERENCE_PATH</key>
				<string></string>
				<key>RELOCATABLE</key>
				<false/>
				<key>USE_HFS+_COMPRESSION</key>
				<false/>
				<key>VERSION</key>
				<string>@MIRALL_VERSION_FULL@</string>
			</dict>
			<key>TYPE</key>
			<integer>0</integer>
			<key>UUID</key>
			<string>7D7219B7-1897-48C3-8533-842BDEC46F71</string>
		</dict>
	</array>
	<key>PROJECT</key>
	<dict>
		<key>PROJECT_COMMENTS</key>
		<dict>
			<key>NOTES</key>
			<data>
			PCFET0NUWVBFIGh0bWwgUFVCTElDICItLy9XM0MvL0RURCBIVE1M
			IDQuMDEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvVFIvaHRtbDQv
			c3RyaWN0LmR0ZCI+CjxodG1sPgo8aGVhZD4KPG1ldGEgaHR0cC1l
			cXVpdj0iQ29udGVudC1UeXBlIiBjb250ZW50PSJ0ZXh0L2h0bWw7
			IGNoYXJzZXQ9VVRGLTgiPgo8bWV0YSBodHRwLWVxdWl2PSJDb250
			ZW50LVN0eWxlLVR5cGUiIGNvbnRlbnQ9InRleHQvY3NzIj4KPHRp
			dGxlPjwvdGl0bGU+CjxtZXRhIG5hbWU9IkdlbmVyYXRvciIgY29u
			dGVudD0iQ29jb2EgSFRNTCBXcml0ZXIiPgo8bWV0YSBuYW1lPSJD
			b2NvYVZlcnNpb24iIGNvbnRlbnQ9IjEzNDMuMTQiPgo8c3R5bGUg
			dHlwZT0idGV4dC9jc3MiPgo8L3N0eWxlPgo8L2hlYWQ+Cjxib2R5
			Pgo8L2JvZHk+CjwvaHRtbD4K
			</data>
		</dict>
		<key>PROJECT_PRESENTATION</key>
		<dict>
			<key>BACKGROUND</key>
			<dict>
				<key>ALIGNMENT</key>
				<integer>6</integer>
				<key>APPAREANCES</key>
				<dict>
					<key>DARK_AQUA</key>
					<dict>
						<key>ALIGNMENT</key>
						<integer>6</integer>
						<key>BACKGROUND_PATH</key>
						<dict>
							<key>PATH</key>
							<string>@MAC_INSTALLER_BACKGROUND_FILE@</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
						</dict>
						<key>CUSTOM</key>
						<true/>
						<key>LAYOUT_DIRECTION</key>
						<integer>0</integer>
						<key>SCALING</key>
						<integer>0</integer>
					</dict>
					<key>LIGHT_AQUA</key>
					<dict>
						<key>ALIGNMENT</key>
						<integer>6</integer>
						<key>BACKGROUND_PATH</key>
						<dict>
							<key>PATH</key>
							<string>@MAC_INSTALLER_BACKGROUND_FILE@</string>
							<key>PATH_TYPE</key>
							<integer>0</integer>
						</dict>
						<key>CUSTOM</key>
						<true/>
						<key>LAYOUT_DIRECTION</key>
						<integer>0</integer>
						<key>SCALING</key>
						<integer>0</integer>
					</dict>
				</dict>
				<key>BACKGROUND_PATH</key>
				<dict>
					<key>PATH</key>
					<string>@MAC_INSTALLER_BACKGROUND_FILE@</string>
					<key>PATH_TYPE</key>
					<integer>0</integer>
				</dict>
				<key>CUSTOM</key>
				<@MAC_INSTALLER_DO_CUSTOM_BACKGROUND@/>
				<key>LAYOUT_DIRECTION</key>
				<integer>0</integer>
				<key>SCALING</key>
				<integer>0</integer>
				<key>SHARED_SETTINGS_FOR_ALL_APPAREANCES</key>
				<true/>
			</dict>
			<key>INSTALLATION TYPE</key>
			<dict>
				<key>HIERARCHIES</key>
				<dict>
					<key>INSTALLER</key>
					<dict>
						<key>LIST</key>
						<array>
							<dict>
								<key>DESCRIPTION</key>
								<array/>
								<key>OPTIONS</key>
								<dict>
									<key>HIDDEN</key>
									<false/>
									<key>STATE</key>
									<integer>1</integer>
								</dict>
								<key>PACKAGE_UUID</key>
								<string>7D7219B7-1897-48C3-8533-842BDEC46F71</string>
								<key>TITLE</key>
								<array/>
								<key>TOOLTIP</key>
								<array/>
								<key>TYPE</key>
								<integer>0</integer>
								<key>UUID</key>
								<string>9647ADC0-BD53-4D7D-A561-73D383AACDE1</string>
							</dict>
						</array>
						<key>REMOVED</key>
						<dict/>
					</dict>
				</dict>
				<key>MODE</key>
				<integer>1</integer>
			</dict>
			<key>INSTALLATION_STEPS</key>
			<array>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewIntroductionController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>Introduction</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewReadMeController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>ReadMe</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewLicenseController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>License</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewDestinationSelectController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>TargetSelect</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewInstallationTypeController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>PackageSelection</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewInstallationController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>Install</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
				<dict>
					<key>ICPRESENTATION_CHAPTER_VIEW_CONTROLLER_CLASS</key>
					<string>ICPresentationViewSummaryController</string>
					<key>INSTALLER_PLUGIN</key>
					<string>Summary</string>
					<key>LIST_TITLE_KEY</key>
					<string>InstallerSectionTitle</string>
				</dict>
			</array>
			<key>INTRODUCTION</key>
			<dict>
				<key>LOCALIZATIONS</key>
				<array/>
			</dict>
			<key>LICENSE</key>
			<dict>
				<key>LOCALIZATIONS</key>
				<array/>
				<key>MODE</key>
				<integer>0</integer>
			</dict>
			<key>README</key>
			<dict>
				<key>LOCALIZATIONS</key>
				<array/>
			</dict>
			<key>SUMMARY</key>
			<dict>
				<key>LOCALIZATIONS</key>
				<array/>
			</dict>
			<key>TITLE</key>
			<dict>
				<key>LOCALIZATIONS</key>
				<array>
					<dict>
						<key>LANGUAGE</key>
						<string>English</string>
						<key>VALUE</key>
						<string>@APPLICATION_NAME@</string>
					</dict>
				</array>
			</dict>
		</dict>
		<key>PROJECT_REQUIREMENTS</key>
		<dict>
			<key>LIST</key>
			<array/>
			<key>RESOURCES</key>
			<array/>
			<key>ROOT_VOLUME_ONLY</key>
			<false/>
		</dict>
		<key>PROJECT_SETTINGS</key>
		<dict>
            <key>ADVANCED_OPTIONS</key>
			<dict>
				<key>installer-script.domains:enable_localSystem</key>
				<integer>1</integer>
			</dict>
			<key>BUILD_FORMAT</key>
			<integer>0</integer>
			<key>BUILD_PATH</key>
			<dict>
				<key>PATH</key>
				<string>@CMAKE_INSTALL_PREFIX@/.</string>
				<key>PATH_TYPE</key>
				<integer>3</integer>
			</dict>
			<key>EXCLUDED_FILES</key>
			<array>
				<dict>
					<key>PATTERNS_ARRAY</key>
					<array>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.DS_Store</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>PROTECTED</key>
					<true/>
					<key>PROXY_NAME</key>
					<string>Remove .DS_Store files</string>
					<key>PROXY_TOOLTIP</key>
					<string>Remove ".DS_Store" files created by the Finder.</string>
					<key>STATE</key>
					<true/>
				</dict>
				<dict>
					<key>PATTERNS_ARRAY</key>
					<array>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.pbdevelopment</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>PROTECTED</key>
					<true/>
					<key>PROXY_NAME</key>
					<string>Remove .pbdevelopment files</string>
					<key>PROXY_TOOLTIP</key>
					<string>Remove ".pbdevelopment" files created by ProjectBuilder or Xcode.</string>
					<key>STATE</key>
					<true/>
				</dict>
				<dict>
					<key>PATTERNS_ARRAY</key>
					<array>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>CVS</string>
							<key>TYPE</key>
							<integer>1</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.cvsignore</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.cvspass</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.svn</string>
							<key>TYPE</key>
							<integer>1</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.git</string>
							<key>TYPE</key>
							<integer>1</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>.gitignore</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>PROTECTED</key>
					<true/>
					<key>PROXY_NAME</key>
					<string>Remove SCM metadata</string>
					<key>PROXY_TOOLTIP</key>
					<string>Remove helper files and folders used by the CVS, SVN or Git Source Code Management systems.</string>
					<key>STATE</key>
					<true/>
				</dict>
				<dict>
					<key>PATTERNS_ARRAY</key>
					<array>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>classes.nib</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>designable.db</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>info.nib</string>
							<key>TYPE</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>PROTECTED</key>
					<true/>
					<key>PROXY_NAME</key>
					<string>Optimize nib files</string>
					<key>PROXY_TOOLTIP</key>
					<string>Remove "classes.nib", "info.nib" and "designable.nib" files within .nib bundles.</string>
					<key>STATE</key>
					<true/>
				</dict>
				<dict>
					<key>PATTERNS_ARRAY</key>
					<array>
						<dict>
							<key>REGULAR_EXPRESSION</key>
							<false/>
							<key>STRING</key>
							<string>Resources Disabled</string>
							<key>TYPE</key>
							<integer>1</integer>
						</dict>
					</array>
					<key>PROTECTED</key>
					<true/>
					<key>PROXY_NAME</key>
					<string>Remove Resources Disabled folders</string>
					<key>PROXY_TOOLTIP</key>
					<string>Remove "Resources Disabled" folders.</string>
					<key>STATE</key>
					<true/>
				</dict>
				<dict>
					<key>SEPARATOR</key>
					<true/>
				</dict>
			</array>
			<key>NAME</key>
			<string>@APPLICATION_NAME@ Installer</string>
			<key>PAYLOAD_ONLY</key>
			<false/>
			<key>REFERENCE_FOLDER_PATH</key>
			<string></string>
			<key>TREAT_MISSING_PRESENTATION_DOCUMENTS_AS_WARNING</key>
			<false/>
		</dict>
	</dict>
	<key>TYPE</key>
	<integer>0</integer>
	<key>VERSION</key>
	<integer>2</integer>
</dict>
</plist>
