/*
 * Copyright (C) by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License
 * for more details.
 */

#ifndef SERVERFWD_H
#define SERVERFWD_H

#include <QPointer>
#include <QSharedPointer>

namespace OCC {

class Account;
class AccountState;

using AccountPtr = QSharedPointer<Account>;
using AccountStatePtr = QPointer<AccountState>;


} // namespace OCC

#endif //SERVERFWD
