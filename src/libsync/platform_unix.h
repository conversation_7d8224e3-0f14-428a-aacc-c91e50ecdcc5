/*
 * Copyright (C) by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License
 * for more details.
 */

#pragma once

#include "platform.h"

#include <csignal>

namespace OCC {

class UnixPlatform : public Platform
{
public:
    ~UnixPlatform() override;

private:
    UnixPlatform(Type t);
    void setLimitsForCoreDumps();

    friend class Platform;
};

} // namespace OCC
