/*
 * Copyright (C) by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License
 * for more details.
 */

#pragma once

#include "platform.h"

#include <QProcess>
#include <qglobal.h>

namespace OCC {

class MacPlatformPrivate;

class MacPlatform : public Platform
{
public:
    ~MacPlatform() override;

    void startServices() override;

private:
    MacPlatform(Type t);

    Q_DECLARE_PRIVATE(MacPlatform)

    QScopedPointer<MacPlatformPrivate> d_ptr;
    friend class Platform;
};

} // namespace OCC
