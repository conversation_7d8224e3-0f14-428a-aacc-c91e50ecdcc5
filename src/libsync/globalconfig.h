// SPDX-License-Identifier: GPL-2.0-or-later
// SPDX-FileCopyrightText: 2025 <PERSON> <h.von<PERSON><EMAIL>>

#pragma once

#include "libsync/opencloudsynclib.h"

#include <QVariant>
#include <QtQmlMeta>

namespace OCC {
class OPENCLOUD_SYNC_EXPORT GlobalConfig : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QUrl serverUrl READ serverUrl CONSTANT)
    QML_ELEMENT
    QML_SINGLETON
public:
    using QObject::QObject;

    static QVariant getValue(QAnyStringView param, const QVariant &defaultValue = {});

#ifdef Q_OS_WIN
    static QVariant getPolicySetting(QAnyStringView policy, const QVariant &defaultValue = {});
#endif

    static QUrl serverUrl();
};
}
