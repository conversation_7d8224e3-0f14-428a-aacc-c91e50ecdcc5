#pragma once

#cmakedefine USE_INOTIFY 1
#cmakedefine WITH_QTKEYCHAIN 1
#cmakedefine WITH_CRASHREPORTER
#cmakedefine CRASHREPORTER_EXECUTABLE "@CRASHREPORTER_EXECUTABLE@"

#cmakedefine THEME_CLASS @THEME_CLASS@
#cmakedefine THEME_INCLUDE "@THEME_INCLUDE@"

#cmakedefine APPLICATION_NAME "@APPLICATION_NAME@"
#cmakedefine APPLICATION_VENDOR "@APPLICATION_VENDOR@"
#cmakedefine APPLICATION_REV_DOMAIN "@APPLICATION_REV_DOMAIN@"
#cmakedefine APPLICATION_SHORTNAME "@APPLICATION_SHORTNAME@"
#cmakedefine APPLICATION_EXECUTABLE "@APPLICATION_EXECUTABLE@"
#cmakedefine APPLICATION_UPDATE_URL "@APPLICATION_UPDATE_URL@"
#cmakedefine APPLICATION_ICON_NAME "@APPLICATION_ICON_NAME@"
