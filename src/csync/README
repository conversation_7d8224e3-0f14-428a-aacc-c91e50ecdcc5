WHAT IS CSYNC?
==============

csync is a lightweight utility to synchronize files between two directories on
a system or between multiple systems.

It synchronizes bidirectionally and allows the user to keep two copies of files
and directories in sync. csync uses widely adopted protocols, such as smb or
sftp, so that there is no need for a server component. It is a user-level
program which means you don’t need to be a superuser or administrator.

CONTRIBUTIONS
=============

If you want to contribute to the development of the software then please join
the mailing list. Patches are accepted preferably created with git and we are
always glad to receive feedback or suggestions to the address
<EMAIL>.
More information on the various mailing lists can be found at
http://www.csync.org/communication/.

You can also get the sourcecode straight from the git repository - see
http://git.csync.org/

DOCUMENTATION
=============

As a user you can find a user-guide which is shipped with this package or is
available at the website. For developers there is doxygen documentation and
comments in the source code itself. See

http://www.csync.org/userguide/
and
http://www.csync.org/api/
