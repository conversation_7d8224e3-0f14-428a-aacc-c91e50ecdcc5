<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AppImageUpdateAvailableWidgetUi</class>
 <widget class="QWidget" name="AppImageUpdateAvailableWidgetUi">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>556</width>
    <height>241</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="sizeConstraint">
      <enum>QLayout::SizeConstraint::SetMinimumSize</enum>
     </property>
     <item>
      <widget class="QLabel" name="appIconLabel"/>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>6</number>
     </property>
     <item>
      <widget class="QLabel" name="installedVersionLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string notr="true">Installed version:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="availableVersionLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string notr="true">Available version:</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Policy::Minimum</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>10</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="infoLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="MinimumExpanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>10</height>
        </size>
       </property>
       <property name="text">
        <string notr="true">Info text placeholder</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="topMargin">
        <number>10</number>
       </property>
       <item>
        <widget class="QPushButton" name="skipButton">
         <property name="text">
          <string>Skip this version</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QDialogButtonBox" name="buttonBox">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="standardButtons">
          <set>QDialogButtonBox::StandardButton::Cancel|QDialogButtonBox::StandardButton::Ok</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
 <slots>
  <slot>accept()</slot>
  <slot>reject()</slot>
 </slots>
</ui>
