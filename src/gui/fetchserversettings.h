/*
 * Copyright (C) by <PERSON> <hannah.von<PERSON><EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License
 * for more details.
 */

#pragma once

#include "gui/connectionvalidator.h"
#include "libsync/accountfwd.h"

#include <QObject>

namespace OCC {
class Capabilities;

class FetchServerSettingsJob : public QObject
{
    Q_OBJECT
public:
    FetchServerSettingsJob(const AccountPtr &account, QObject *parent);

    void start();

Q_SIGNALS:
    void finishedSignal();

private:
    void runAsyncUpdates();

    const AccountPtr _account;
};

}
