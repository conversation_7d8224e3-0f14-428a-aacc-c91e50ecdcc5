<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SpacesBrowser</class>
 <widget class="QWidget" name="SpacesBrowser">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="OCC::QmlUtils::OCQuickWidget" name="quickWidget" native="true">
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::StrongFocus</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>OCC::QmlUtils::OCQuickWidget</class>
   <extends>QWidget</extends>
   <header>gui/qmlutils.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
