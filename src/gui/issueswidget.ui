<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::IssuesWidget</class>
 <widget class="QWidget" name="OCC::IssuesWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>580</width>
    <height>578</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="_filterButton"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableView" name="_tableView">
     <property name="contextMenuPolicy">
      <enum>Qt::ContextMenuPolicy::CustomContextMenu</enum>
     </property>
     <property name="accessibleName">
      <string>Issues table</string>
     </property>
     <property name="tabKeyNavigation">
      <bool>false</bool>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
     </property>
     <property name="showGrid">
      <bool>false</bool>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
     <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
      <bool>true</bool>
     </attribute>
     <attribute name="verticalHeaderVisible">
      <bool>false</bool>
     </attribute>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="_tooManyIssuesWarning">
         <property name="text">
          <string>There were too many issues. Not all will be visible here.</string>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>_filterButton</tabstop>
  <tabstop>_tableView</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
