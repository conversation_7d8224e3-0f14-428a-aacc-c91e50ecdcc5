<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::ProtocolWidget</class>
 <widget class="QWidget" name="OCC::ProtocolWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>612</width>
    <height>515</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="_filterButton">
       <property name="text">
        <string>Filter</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableView" name="_tableView">
     <property name="contextMenuPolicy">
      <enum>Qt::CustomContextMenu</enum>
     </property>
     <property name="accessibleName">
      <string>Local activity table</string>
     </property>
     <property name="tabKeyNavigation">
      <bool>false</bool>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <property name="showGrid">
      <bool>false</bool>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
     <property name="wordWrap">
      <bool>false</bool>
     </property>
     <attribute name="verticalHeaderVisible">
      <bool>false</bool>
     </attribute>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>_filterButton</tabstop>
  <tabstop>_tableView</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
