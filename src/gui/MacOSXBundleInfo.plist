<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
        <key>NSPrincipalClass</key>
        <string>NSApplication</string>
        <key>CFBundleDevelopmentRegion</key>
        <string>English</string>
        <key>CFBundleName</key>
        <string>${APPLICATION_NAME}</string>
        <key>CFBundleExecutable</key>
        <string>${APPLICATION_SHORTNAME}</string>
        <key>CFBundleIconFile</key>
        <string>${MACOSX_BUNDLE_ICON_FILE}</string>
        <key>CFBundleIdentifier</key>
        <string>${APPLICATION_REV_DOMAIN}</string>
        <key>CFBundleInfoDictionaryVersion</key>
        <string>6.0</string>
        <key>CFBundleLongVersionString</key>
        <string>${APPLICATION_NAME} ${MIRALL_VERSION_STRING}</string>
        <key>CFBundlePackageType</key>
        <string>APPL</string>
        <key>CFBundleSignature</key>
        <string>????</string>
        <key>CFBundleVersion</key>
        <string>${MIRALL_VERSION_FULL}</string>
        <key>CFBundleShortVersionString</key>
        <string>${MIRALL_VERSION_STRING}</string>
        <key>NSHumanReadableCopyright</key>
        <string>(C) 2025 OpenCloud GmbH
(C) 2014-${MIRALL_VERSION_YEAR} ownCloud GmbH</string>
        <key>SUShowReleaseNotes</key>
        <false/>
        <key>SUPublicDSAKeyFile</key>
        <string>dsa_pub.pem</string>
        <key>SUPublicEDKey</key>
        <string>F9ZGRXJE7XbQb2Kt36hwaBO4rYjkXYHiNS5hd+MkyKY=</string>
        <key>SUAllowsAutomaticUpdates</key>
        <string>NO</string>
        <key>LSMinimumSystemVersion</key>
        <string>${CMAKE_OSX_DEPLOYMENT_TARGET}</string>
        <key>CFBundleAllowMixedLocalizations</key>
        <true/>
</dict>
</plist>
