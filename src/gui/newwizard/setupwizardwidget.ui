<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SetupWizardWidget</class>
 <widget class="QWidget" name="SetupWizardWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>750</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Add New Account</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QStackedWidget" name="contentWidget">
     <widget class="QWidget" name="transitionPage">
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QProgressIndicator" name="transitionProgressIndicator" native="true"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>9</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <property name="bottomMargin">
      <number>9</number>
     </property>
     <item>
      <widget class="QWidget" name="errorMessageWidget" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0">
        <item>
         <widget class="QLabel" name="warningPixmap">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="pixmap">
           <pixmap resource="../../resources/client.qrc">:/client/resources/light/warning.svg</pixmap>
          </property>
          <property name="scaledContents">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="errorMessageLabel">
          <property name="textFormat">
           <enum>Qt::RichText</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="errorMessageDismissButton">
          <property name="text">
           <string>&amp;Dismiss</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="bottomButtonsLayout">
       <property name="topMargin">
        <number>9</number>
       </property>
       <item>
        <widget class="QPushButton" name="cancelButton">
         <property name="text">
          <string>&amp;Cancel</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="OCC::Wizard::Navigation" name="navigation" native="true"/>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="backButton">
         <property name="text">
          <string>&lt; Back</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="nextButton">
         <property name="text">
          <string notr="true">placeholder</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>OCC::Wizard::Navigation</class>
   <extends>QWidget</extends>
   <header>navigation.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>QProgressIndicator</class>
   <extends>QWidget</extends>
   <header>3rdparty/QProgressIndicator/QProgressIndicator.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../resources/client.qrc"/>
 </resources>
 <connections/>
</ui>
