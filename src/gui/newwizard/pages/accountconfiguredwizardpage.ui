<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AccountConfiguredWizardPage</class>
 <widget class="QWidget" name="AccountConfiguredWizardPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>460</width>
    <height>432</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::MinimumExpanding</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="youreAllSetLabel">
     <property name="text">
      <string>✓ You're all set!</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::MinimumExpanding</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QGroupBox" name="advancedConfigGroupBox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>Advanced configuration</string>
     </property>
     <property name="flat">
      <bool>false</bool>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <property name="checked">
      <bool>false</bool>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>60</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>24</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="advancedConfigGroupBoxContentWidget" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <property name="spacing">
          <number>18</number>
         </property>
         <property name="leftMargin">
          <number>9</number>
         </property>
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="rightMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>9</number>
         </property>
         <item>
          <widget class="QGroupBox" name="syncModeGroupBox">
           <property name="title">
            <string/>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
           <layout class="QVBoxLayout" name="syncModeGroupBoxLayout">
            <property name="spacing">
             <number>3</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label">
              <property name="text">
               <string>Configure files download:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="syncEverythingRadioButton">
              <property name="text">
               <string>Download everything</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="configureSyncManuallyRadioButton">
              <property name="toolTip">
               <string>After completing this wizard, you can set up folder synchronization manually.</string>
              </property>
              <property name="text">
               <string>Configure synchronization manually</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="useVfsRadioButton">
              <property name="text">
               <string notr="true">Enable virtual filesystem (placeholder)</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="localDirectoryGroupBox">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QGroupBox {border:0;}</string>
           </property>
           <property name="title">
            <string notr="true"/>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="chooseLocalDownloadDirectoryLabel">
              <property name="text">
               <string>Choose local download directory:</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QLineEdit" name="localDirectoryLineEdit"/>
              </item>
              <item>
               <widget class="QToolButton" name="chooseLocalDirectoryButton">
                <property name="text">
                 <string notr="true"/>
                </property>
                <property name="icon">
                 <iconset resource="../../../resources/client.qrc">
                  <normaloff>:/client/resources/core/more.svg</normaloff>:/client/resources/core/more.svg</iconset>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QToolButton" name="resetLocalDirectoryButton">
                <property name="toolTip">
                 <string>Restore default value</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="icon">
                 <iconset resource="../../../resources/client.qrc">
                  <normaloff>:/client/resources/core/undo.svg</normaloff>:/client/resources/core/undo.svg</iconset>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../resources/client.qrc"/>
 </resources>
 <connections/>
</ui>
