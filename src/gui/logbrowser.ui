<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::<PERSON>gBrowser</class>
 <widget class="QDialog" name="OCC::LogBrowser">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>399</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Log Output</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="warningIcon">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>64</width>
         <height>64</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>64</width>
         <height>64</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../resources/client.qrc">:/client/resources/light/warning.svg</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;b&gt;The logs contain sensitive information which you should not make publicly available&lt;/b&gt;&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="MinimumExpanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>The client can write debug logs to a temporary folder. These logs are very helpful for diagnosing problems.
Since log files can get large, the client will start a new one for each sync run and compress older ones.</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>If enabled, logs will be written to:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="locationLabel">
       <property name="text">
        <string>C:/log</string>
       </property>
       <property name="textInteractionFlags">
        <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QCheckBox" name="enableLoggingButton">
     <property name="text">
      <string>Enable logging to temporary folder</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QCheckBox" name="httpLogButton">
     <property name="text">
      <string>Log Http traffic </string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>Log files to keep:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox_numberOflogsToKeep">
       <property name="minimum">
        <number>5</number>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="label_3">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="MinimumExpanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>These settings persist across client restarts.
Note that using any logging command line options will override the settings.</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="openFolderButton">
     <property name="text">
      <string>Open folder</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/client.qrc"/>
 </resources>
 <connections/>
</ui>
