<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::GeneralSettings</class>
 <widget class="QWidget" name="OCC::GeneralSettings">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>791</width>
    <height>686</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents_2">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>765</width>
        <height>618</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QGroupBox" name="generalGroupBox">
         <property name="title">
          <string>General Settings</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QLabel" name="languageLabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="locale">
               <locale language="English" country="UnitedStates"/>
              </property>
              <property name="text">
               <string>Language</string>
              </property>
              <property name="buddy">
               <cstring>languageDropdown</cstring>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="languageDropdown">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="accessibleName">
               <string>Language selector</string>
              </property>
              <property name="currentText">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="0">
           <widget class="QCheckBox" name="autostartCheckBox">
            <property name="text">
             <string>Start on Login</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="advancedGroupBox">
         <property name="title">
          <string>Advanced</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_6">
            <item>
             <widget class="QCheckBox" name="syncHiddenFilesCheckBox">
              <property name="text">
               <string>Sync hidden files</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="crashreporterCheckBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>Show crash reporter</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="moveToTrashCheckBox">
              <property name="text">
               <string>Move remotely deleted files to the local trash bin instead of deleting them</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QPushButton" name="ignoredFilesButton">
                <property name="text">
                 <string>Edit Ignored Files</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="logSettingsButton">
                <property name="text">
                 <string>Log Settings</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="spacer_3">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>555</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>Network</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="NetworkSettings" name="widget" native="true">
            <property name="focusPolicy">
             <enum>Qt::FocusPolicy::StrongFocus</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="versionInfoWidget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="spacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="about_pushButton">
        <property name="text">
         <string>About</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>NetworkSettings</class>
   <extends>QWidget</extends>
   <header>networksettings.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <tabstops>
  <tabstop>autostartCheckBox</tabstop>
  <tabstop>languageDropdown</tabstop>
  <tabstop>syncHiddenFilesCheckBox</tabstop>
  <tabstop>crashreporterCheckBox</tabstop>
  <tabstop>moveToTrashCheckBox</tabstop>
  <tabstop>ignoredFilesButton</tabstop>
  <tabstop>logSettingsButton</tabstop>
  <tabstop>widget</tabstop>
  <tabstop>scrollArea</tabstop>
  <tabstop>about_pushButton</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
