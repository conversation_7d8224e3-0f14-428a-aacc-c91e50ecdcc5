<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FolderWizardSourcePage</class>
 <widget class="QWidget" name="FolderWizardSourcePage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>423</width>
    <height>174</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Select a local folder to synchronize your Spaces to:</string>
     </property>
     <property name="buddy">
      <cstring>localFolderLineEdit</cstring>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLineEdit" name="localFolderLineEdit">
       <property name="toolTip">
        <string>Enter the path to the Spaces root folder. This folder will contain all your synchronized Spaces.</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="localFolderChooseBtn">
       <property name="toolTip">
        <string>Click to select a the Spaces root folder.</string>
       </property>
       <property name="text">
        <string>&amp;Choose...</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="warnLabel">
     <property name="text">
      <string/>
     </property>
     <property name="textFormat">
      <enum>Qt::TextFormat::RichText</enum>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>349</width>
       <height>0</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
