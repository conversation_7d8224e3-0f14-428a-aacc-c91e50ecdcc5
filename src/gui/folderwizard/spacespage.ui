<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SpacesPage</class>
 <widget class="QWizardPage" name="SpacesPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>362</width>
    <height>300</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Choose a Space to sync</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="OCC::Spaces::SpacesBrowser" name="widget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::StrongFocus</enum>
     </property>
     <property name="accessibleName">
      <string>Spaces list</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>OCC::Spaces::SpacesBrowser</class>
   <extends>QWidget</extends>
   <header>gui/spaces/spacesbrowser.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
