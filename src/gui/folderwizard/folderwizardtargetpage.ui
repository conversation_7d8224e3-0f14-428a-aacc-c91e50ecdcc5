<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FolderWizardTargetPage</class>
 <widget class="QWidget" name="FolderWizardTargetPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>520</width>
    <height>350</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Select a remote destination folder</string>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
     <property name="checkable">
      <bool>false</bool>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QTreeWidget" name="folderTreeWidget">
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <property name="headerHidden">
         <bool>true</bool>
        </property>
        <column>
         <property name="text">
          <string>Folders</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QPushButton" name="addFolderButton">
          <property name="text">
           <string>Create Folder</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="refreshButton">
          <property name="text">
           <string>Refresh</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QLineEdit" name="folderEntry"/>
   </item>
   <item>
    <widget class="QFrame" name="warningFrame">
     <property name="palette">
      <palette>
       <active/>
       <inactive/>
       <disabled/>
      </palette>
     </property>
     <property name="autoFillBackground">
      <bool>true</bool>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="warningIcon">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Plain</enum>
          </property>
          <property name="textFormat">
           <enum>Qt::AutoText</enum>
          </property>
          <property name="pixmap">
           <pixmap resource="../../resources/client.qrc">:/client/resources/light/warning.svg</pixmap>
          </property>
          <property name="scaledContents">
           <bool>true</bool>
          </property>
          <property name="wordWrap">
           <bool>false</bool>
          </property>
          <property name="margin">
           <number>0</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warningLabel">
          <property name="text">
           <string/>
          </property>
          <property name="textFormat">
           <enum>Qt::RichText</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../resources/client.qrc"/>
 </resources>
 <connections/>
</ui>
