<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::AccountSettings</class>
 <widget class="QWidget" name="OCC::AccountSettings">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>678</width>
    <height>557</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>5</verstretch>
      </sizepolicy>
     </property>
     <property name="currentIndex">
      <number>1</number>
     </property>
     <widget class="QWidget" name="loadingPage">
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Preparing the account</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QProgressIndicator" name="spinner" native="true"/>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="OCC::QmlUtils::OCQuickWidget" name="quickWidget">
      <property name="accessibleName">
       <string>Sync connections</string>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>OCC::QmlUtils::OCQuickWidget</class>
   <extends>QWidget</extends>
   <header>gui/qmlutils.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>QProgressIndicator</class>
   <extends>QWidget</extends>
   <header>QProgressIndicator.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
