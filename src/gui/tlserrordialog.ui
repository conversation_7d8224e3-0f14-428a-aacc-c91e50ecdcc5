<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::TlsErrorDialog</class>
 <widget class="QDialog" name="OCC::TlsErrorDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>534</width>
    <height>493</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>TLS Certificate Error</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="hostnameLabel">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string notr="true">Cannot connect securely to host hostname (placeholder)</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QTextBrowser" name="textBrowser"/>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>Do you want to trust this certificate anyway?</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::No|QDialogButtonBox::Yes</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
