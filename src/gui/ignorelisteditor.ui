<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OCC::IgnoreListEditor</class>
 <widget class="QDialog" name="OCC::IgnoreListEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>555</width>
    <height>609</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Ignored Files Editor</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>Files Ignored by Patterns</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QTableWidget" name="tableWidget">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="selectionMode">
           <enum>QAbstractItemView::SingleSelection</enum>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <property name="columnCount">
           <number>2</number>
          </property>
          <column>
           <property name="text">
            <string>Pattern</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Allow Deletion</string>
           </property>
          </column>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <widget class="QPushButton" name="addPushButton">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>Add</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="removePushButton">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>Remove</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>213</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="descriptionLabel">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Files or folders matching a pattern will not be synchronized. Changes take effect the next time folders are synchronized.

Items where deletion is allowed will be deleted if they prevent a directory from being removed. This is useful for meta data.</string>
        </property>
        <property name="textFormat">
         <enum>Qt::PlainText</enum>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>OCC::IgnoreListEditor</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>348</x>
     <y>333</y>
    </hint>
    <hint type="destinationlabel">
     <x>361</x>
     <y>355</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>OCC::IgnoreListEditor</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>210</x>
     <y>333</y>
    </hint>
    <hint type="destinationlabel">
     <x>385</x>
     <y>297</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
