/*
 * Copyright (C) by <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) by <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include "utility.h"

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QSettings>
#include <QStandardPaths>
#include <QString>
#include <QTextStream>

namespace OCC {

void Utility::setupFavLink(const QString &folder)
{
    // Nautilus: add to ~/.gtk-bookmarks
    QFile gtkBookmarks(QDir::homePath() + QLatin1String("/.config/gtk-3.0/bookmarks"));
    const QByteArray folderUrl = QUrl::fromLocalFile(folder).toEncoded(QUrl::ComponentFormattingOption::EncodeSpaces);
    if (gtkBookmarks.open(QFile::ReadWrite)) {
        QByteArray places = gtkBookmarks.readAll();
        if (!places.contains(folderUrl)) {
            places += folderUrl;
            gtkBookmarks.reset();
            gtkBookmarks.write(places + '\n');
        }
    }
}

// returns the autostart directory the linux way
// and respects the XDG_CONFIG_HOME env variable
static QString getUserAutostartDir()
{
    QString config = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    config += QLatin1String("/autostart/");
    return config;
}

bool Utility::hasSystemLaunchOnStartup(const QString &appName)
{
    Q_UNUSED(appName)
    return false;
}

bool Utility::hasLaunchOnStartup(const QString &appName)
{
    QString desktopFileLocation = getUserAutostartDir() + appName + QLatin1String(".desktop");
    return QFile::exists(desktopFileLocation);
}

void Utility::setLaunchOnStartup(const QString &appName, const QString &guiName, bool enable)
{
    QString userAutoStartPath = getUserAutostartDir();
    QString desktopFileLocation = userAutoStartPath + appName + QLatin1String(".desktop");
    if (enable) {
        if (!QDir().exists(userAutoStartPath) && !QDir().mkpath(userAutoStartPath)) {
            qCWarning(lcUtility) << "Could not create autostart folder" << userAutoStartPath;
            return;
        }
        QFile iniFile(desktopFileLocation);
        if (!iniFile.open(QIODevice::WriteOnly)) {
            qCWarning(lcUtility) << "Could not write auto start entry" << desktopFileLocation;
            return;
        }

        auto autostartApplicationPath = []() -> QString {
            // $APPIMAGE will be set to the AppImage's path by the AppImage runtime
            // if it is set, we can assume to be run from within an AppImage
            // in that case, the desktop file should point to the AppImage rather than the
            // main binary, which will be in a temporary mount point
            if (Utility::runningInAppImage()) {
                return Utility::appImageLocation();
            }

            // we need to launch the client via its provided AppRun script (or at least source the AppRun hooks) to make sure it is displayed correctly
            // if installed as a native package generated by linuxdeploy-plugin-native_packages, a linuxdeploy.conf file will be available that points to the
            // location of the installed AppDir
            QString linuxdeployConfPath = qApp->applicationDirPath() + QStringLiteral("/linuxdeploy.conf");

            if (QFile(linuxdeployConfPath).exists()) {
                QSettings linuxdeployConf(linuxdeployConfPath, QSettings::IniFormat);

                const auto appdirInstallatedPath = linuxdeployConf.value(QStringLiteral("native_packages/appdir_installed_path"));
                if (!appdirInstallatedPath.isNull()) {
                    return appdirInstallatedPath.toString() + QStringLiteral("/AppRun");
                }
            }

            // otherwise, we just use the application binary's own path, which should work for distribution-packaged installations and also for development
            return QCoreApplication::applicationFilePath();
        }();

        QTextStream ts(&iniFile);
        ts.setEncoding(QStringConverter::Utf8);
        ts << QLatin1String("[Desktop Entry]") << Qt::endl
           << QLatin1String("Name=") << guiName << Qt::endl
           << QLatin1String("GenericName=") << QLatin1String("File Synchronizer") << Qt::endl
           << QLatin1String("Exec=") << autostartApplicationPath << Qt::endl
           << QLatin1String("Terminal=") << "false" << Qt::endl
           << QLatin1String("Icon=") << appName.toLower() << Qt::endl // always use lowercase for icons
           << QLatin1String("Categories=") << QLatin1String("Network") << Qt::endl
           << QLatin1String("Type=") << QLatin1String("Application") << Qt::endl
           << QLatin1String("StartupNotify=") << "false" << Qt::endl
           << QLatin1String("X-GNOME-Autostart-enabled=") << "true" << Qt::endl
           << QLatin1String("X-GNOME-Autostart-Delay=10") << Qt::endl;
    } else {
        if (!QFile::remove(desktopFileLocation)) {
            qCWarning(lcUtility) << "Could not remove autostart desktop file";
        }
    }
}

bool Utility::hasDarkSystray()
{
    return true;
}

} // namespace OCC
