/*
 * Copyright (C) by <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include "common/version.h"

const QVersionNumber &OCC::Version::version()
{
    static const auto v = QVersionNumber({ @MIRALL_VERSION_MAJOR@, @MIRALL_VERSION_MINOR@, @MIRALL_VERSION_PATCH@ });
    return v;
}

const QVersionNumber &OCC::Version::versionWithBuildNumber()
{
    static const auto v = QVersionNumber({ @MIRALL_VERSION_MAJOR@, @MIRALL_VERSION_MINOR@, @MIRALL_VERSION_PATCH@, @MIRALL_VERSION_BUILD@ });
    return v;
}

QString OCC::Version::gitSha()
{
    return QStringLiteral("@GIT_SHA1@");
}

QString OCC::Version::suffix()
{
    return QStringLiteral("@MIRALL_VERSION_SUFFIX@");
}

QString OCC::Version::displayString()
{
    // ensure a well-defined version string
    constexpr std::string_view versionString("@MIRALL_VERSION_STRING@");
    constexpr std::string_view versionSuffix("@MIRALL_VERSION_SUFFIX@");
    static_assert(versionString.find("@MIRALL_VERSION_MAJOR@.@MIRALL_VERSION_MINOR@.@MIRALL_VERSION_PATCH@") == 0);
    static_assert(versionString.rfind("@MIRALL_VERSION_SUFFIX@") == versionString.size() - versionSuffix.size());
    return QStringLiteral("@MIRALL_VERSION_STRING@");
}