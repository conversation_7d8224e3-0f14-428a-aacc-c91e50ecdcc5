/*
 * Copyright (C) by <PERSON> <hannah.von<PERSON><EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License
 * for more details.
 */

#pragma once

#include "ocsynclib.h"

#include <QStringList>

#include <functional>

namespace OCC {
class OCSYNC_EXPORT RestartManager
{
public:
    RestartManager(std::function<int(int, char **)> &&main);

    ~RestartManager();

    int exec(int argc, char **argv) const;

    static void requestRestart();

private:
    static RestartManager *_instance;

    std::function<int(int, char **)> _main;

    QString _applicationToRestart;
    QStringList _args;
};

}
