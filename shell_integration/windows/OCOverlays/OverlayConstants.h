/**
* Copyright (c) 2000-2013 Liferay, Inc. All rights reserved.
*
* This library is free software; you can redistribute it and/or modify it under
* the terms of the GNU Lesser General Public License as published by the Free
* Software Foundation; either version 2.1 of the License, or (at your option)
* any later version.
*
* This library is distributed in the hope that it will be useful, but WITHOUT
* ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
* FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
* details.
*/


#define OVERLAY_GUID_ERROR          L"{0960F090-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_ERROR_SHARED   L"{0960F091-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_OK             L"{0960F092-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_OK_SHARED      L"{0960F093-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_SYNC           L"{0960F094-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_SYNC_SHARED    L"{0960F095-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_WARNING        L"{0960F096-F328-48A3-B746-276B1E3C3722}"
#define OVERLAY_GUID_WARNING_SHARED L"{0960F097-F328-48A3-B746-276B1E3C3722}"

#define OVERLAY_GENERIC_NAME L"OpenCloud overlay handler"

// two spaces to put us ahead of the competition :/
#define OVERLAY_NAME_ERROR          L"  OCError"
#define OVERLAY_NAME_ERROR_SHARED   L"  OCErrorShared"
#define OVERLAY_NAME_OK             L"  OCOK"
#define OVERLAY_NAME_OK_SHARED      L"  OCOKShared"
#define OVERLAY_NAME_SYNC           L"  OCSync"
#define OVERLAY_NAME_SYNC_SHARED    L"  OCSyncShared"
#define OVERLAY_NAME_WARNING        L"  OCWarning"
#define OVERLAY_NAME_WARNING_SHARED L"  OCWarningShared"

#define REGISTRY_OVERLAY_KEY        L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\ShellIconOverlayIdentifiers"
#define REGISTRY_CLSID              L"CLSID"
#define REGISTRY_IN_PROCESS         L"InprocServer32"
#define REGISTRY_THREADING          L"ThreadingModel"
#define REGISTRY_APARTMENT          L"Apartment"
#define REGISTRY_VERSION            L"Version"
#define REGISTRY_VERSION_NUMBER     L"1.0"

//Registry values for running
#define REGISTRY_ENABLE_OVERLAY     L"EnableOverlay"

#define GET_FILE_OVERLAY_ID     L"getFileIconId"

#define PORT                34001
