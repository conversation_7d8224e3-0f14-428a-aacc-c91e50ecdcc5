/**
 *  Copyright (c) 2000-2013 Liferay, Inc. All rights reserved.
 *  
 *  This library is free software; you can redistribute it and/or modify it under
 *  the terms of the GNU Lesser General Public License as published by the Free
 *  Software Foundation; either version 2.1 of the License, or (at your option)
 *  any later version.
 *  
 *  This library is distributed in the hope that it will be useful, but WITHOUT
 *  ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 *  FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 *  details.
 */

#define PLUG_IN_SOCKET_ADDRESS			"127.0.0.1"

#define BACK_SLASH						L"\\"
#define CLOSE_BRACE						L"]"
#define CLOSE_CURLY_BRACE				L"}"
#define COLON							L":"
#define COMMAND							L"command"
#define COMMA							L","
#define OPEN_BRACE						L"["
#define OPEN_CURLY_BRACE				L"{"
#define QUOTE							L"\""
#define VALUE							L"value"

#define REGISTRY_ENABLE_OVERLAY		 L"EnableOverlay"
#define REGISTRY_FILTER_FOLDER		 L"FilterFolder"
