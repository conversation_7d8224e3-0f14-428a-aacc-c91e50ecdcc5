name: "Update translations"

on:
  workflow_dispatch:
  schedule:
    - cron: "0 3 * * *"

permissions: {}

defaults:
  run:
    shell: pwsh

jobs:
  update-translations:
    permissions:
      # for git push
      contents: write
    runs-on: ubuntu-latest

    env:
      TX_TOKEN: ${{ secrets.TX_TOKEN }}

    steps:
      - uses: actions/checkout@v4
        with:
          ssh-key: ${{ secrets.DEPLOYMENT_SSH_KEY }}
          submodules: recursive
          ref: "main"

      - name: install-deps
        run: sudo apt install -y qt6-l10n-tools

      - name: l10n-remove-old
        run: |
          rm translations/*.ts
          git checkout translations/desktop_en.ts

      - name: l10n-read
        run: /usr/lib/qt6/bin/lupdate src -no-obsolete -ts translations/desktop_en.ts

      - name: l10n-push-source
        uses: transifex/cli-action@v2
        with:
          token: ${{ secrets.TX_TOKEN }}
          args: push -s --silent

      - name: fix-transifex-action
        run: rm -rf /tmp/tx

      - name: l10n-pull
        uses: transifex/cli-action@v2
        with:
          token: ${{ secrets.TX_TOKEN }}
          args: pull --force --all --silent

      - uses: GuillaumeFalourd/git-commit-push@v1.3
        with:
          email: <EMAIL>
          name: OpenCloud
          commit_message: "[tx] updated translations from transifex"
