name: "Close stale issues"
on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

permissions: {}

jobs:
  stale:
    permissions:
      contents: write # only for delete-branch option
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
    - uses: actions/stale@v9
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        debug-only: false
        exempt-all-milestones: true
        exempt-issue-labels: "blue-ticket, p1-urgent, p2-high, p3-medium, p4-low, QA:team"
        days-before-stale: 30
        stale-issue-message: "This issue was marked stale because it has been open for 30 days with no activity. Remove the stale label or comment or this will be closed in 7 days."
        days-before-close: 7
        close-issue-message: "The issue was marked as stale for 7 days and closed automatically."
        start-date: "2021-01-01T00:00:00Z"
        close-issue-reason: not_planned
        # do not close any PRs
        only-pr-labels: "never-close-any-prs"
