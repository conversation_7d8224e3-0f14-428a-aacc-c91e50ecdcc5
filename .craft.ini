[General]
ShallowClone = False

# Variables defined here override the default value
# The variable names are case-sensitive
[Variables]
#Values need to be overwritten to create a cache
UseCache = True
CreateCache = True

OpenCloudVersion = 1.0
# this version must match the target in the blueprint

# update MacDeploymentTarget when updating Qt
#QtVersion = 6.8.0
CachePatchLvl = 0

# set WORKSPACE to cwd, override on ci
# with legacy fallback APPVEYOR_BUILD_FOLDER
WORKSPACE = ${Variables:APPVEYOR_BUILD_FOLDER}
APPVEYOR_BUILD_FOLDER = ${Variables:Root}
CiBuild = True

# Settings applicable for all Crafts matrices
# Settings are Category/key=value
# Category is case sensitive

[GeneralSettings]
Version/ConfigVersion = 6

Packager/Destination=${Variables:WORKSPACE}/binaries
Paths/Python = C:/Python36
Paths/Python27 = C:/Python27
Paths/DownloadDir = ${Variables:Root}/downloads
Blueprints/BlueprintRoot = ${Variables:Root}/blueprints
CraftDebug/LogDir = ${Variables:Root}/logs
ShortPath/JunctionDir = /_/
Packager/CacheDir = ${Variables:WORKSPACE}/cache
Packager/UseCache = ${Variables:UseCache}
Packager/CreateCache = ${Variables:CreateCache}
#Packager/CacheVersion = ${Variables:OpenCloudVersion}/Qt_${Variables:QtVersion}_${Variables:CachePatchLvl}
Packager/PackageDebugSymbols = ${Env:CRAFT_PACKAGE_SYMBOLS}
Packager/DownloadDebugSymbolsCache = ${Env:CRAFT_PACKAGE_SYMBOLS}
Packager/AppxPublisherId = CN=44DA8A24-8237-46B0-B4A2-D297A6A22705
Compile/BuildType = RelWithDebInfo
ContinuousIntegration/Enabled = ${Variables:CiBuild}
ContinuousIntegration/UpdateRepository = True
CodeSigning/Enabled = ${Env:SIGN_PACKAGE}
CodeSigning/Protected = True
CodeSigning/Certificate = ${Env:CRAFT_CODESIGN_CERTIFICATE}
CodeSigning/CommonName =
CodeSigning/MacDeveloperId = OpenCloud GmbH (8P6LQ2M542)
CodeSigning/MacAppleID = <EMAIL>
CodeSigning/MacKeychainPath = sign-${Env:DRONE_BUILD_NUMBER}.keychain

[BlueprintSettings]
# skip msys in normal builds its only needed for the cache
dev-utils/msys-base.ignored = True
dev-utils/msys.ignored = True
# in the current cache that pulls in the uncached lxml which takes ages to build
dev-utils/gtk-doc.ignored = True

#libs/qt6.version = ${Variables:QtVersion}

[windows-cl-msvc2022-x86_64]
General/ABI = windows-cl-msvc2022-x86_64

[windows-cl-msvc2022-x86_64-debug]
General/ABI = windows-cl-msvc2022-x86_64
Compile/BuildType = Debug

[macos-clang-x86_64]
General/ABI = macos-clang-x86_64
# Packager/PackageType = MacPkgPackager

[macos-clang-x86_64-debug]
General/ABI = macos-clang-x86_64
Compile/BuildType = Debug

[macos-clang-arm64]
General/ABI = macos-clang-arm64

[macos-clang-arm64-debug]
General/ABI = macos-clang-arm64
Compile/BuildType = Debug

[linux-gcc-x86_64]
General/ABI = linux-gcc-x86_64

[linux-gcc-x86_64-BlueprintSettings]
libs/qt6/qtbase.withDBus = True

[linux-gcc-x86_64-debug]
General/ABI = linux-gcc-x86_64
Compile/BuildType = Debug

[linux-gcc-x86_64-debug-BlueprintSettings]
libs/qt6/qtbase.withDBus = True

[Env]
CRAFT_PACKAGE_SYMBOLS = False
CRAFT_CODESIGN_CERTIFICATE =
SIGN_PACKAGE = False
# double fallback
DRONE_BUILD_NUMBER = ${Env:APPVEYOR_BUILD_NUMBER}
APPVEYOR_BUILD_NUMBER = 0
