lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

devDependencies:
  '@playwright/test':
    specifier: 1.45.0
    version: 1.45.0

packages:

  /@playwright/test@1.45.0:
    resolution: {integrity: sha512-TVYsfMlGAaxeUllNkywbwek67Ncf8FRGn8ZlRdO291OL3NjG9oMbfVhyP82HQF0CZLMrYsvesqoUekxdWuF9Qw==}
    engines: {node: '>=18'}
    hasBin: true
    dependencies:
      playwright: 1.45.0
    dev: true

  /fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /playwright-core@1.45.0:
    resolution: {integrity: sha512-lZmHlFQ0VYSpAs43dRq1/nJ9G/6SiTI7VPqidld9TDefL9tX87bTKExWZZUF5PeRyqtXqd8fQi2qmfIedkwsNQ==}
    engines: {node: '>=18'}
    hasBin: true
    dev: true

  /playwright@1.45.0:
    resolution: {integrity: sha512-4z3ac3plDfYzGB6r0Q3LF8POPR20Z8D0aXcxbJvmfMgSSq1hkcgvFRXJk9rUq5H/MJ0Ktal869hhOdI/zUTeLA==}
    engines: {node: '>=18'}
    hasBin: true
    dependencies:
      playwright-core: 1.45.0
    optionalDependencies:
      fsevents: 2.3.2
    dev: true
