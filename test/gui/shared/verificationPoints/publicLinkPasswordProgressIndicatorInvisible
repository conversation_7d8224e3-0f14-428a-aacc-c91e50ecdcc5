<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<VerificationPoint type="Visual" version="5">
    <Description/>
    <Verification object="oCC_ShareLinkWidget_checkBox_password_QProgressIndicator">
        <visual>
            <ui version="1.0">
                <execution>
                    <state>
                        <element id="1790cb02503" class="QProgressIndicator">
                            <image type="PNG">iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAIklEQVQ4jWN8//79fwYqAiZqGjZq4KiBowaOGjhq4FAyEACDpgP0OKH6SQAAAABJRU5ErkJggg==</image>
                            <superclass>
                                <class>QWidget</class>
                                <class>QObject</class>
                            </superclass>
                            <abstractProperties>
                                <visual isTopLevel="1"/>
                                <geometry isRelative="0">
                                    <x>2739</x>
                                    <y>457</y>
                                    <height>20</height>
                                    <width>20</width>
                                </geometry>
                            </abstractProperties>
                            <properties>
                                <property name="focus">
                                    <string>false</string>
                                </property>
                                <property name="fullScreen">
                                    <string>false</string>
                                </property>
                                <property name="windowFilePath"/>
                                <property name="delay">
                                    <string>40</string>
                                </property>
                                <property name="toolTipDuration">
                                    <string>-1</string>
                                </property>
                                <property name="displayedWhenStopped">
                                    <string>false</string>
                                </property>
                                <property name="whatsThis"/>
                                <property name="focusPolicy">
                                    <string>0</string>
                                </property>
                                <property name="windowTitle"/>
                                <property name="windowOpacity">
                                    <string>1</string>
                                </property>
                                <property name="enabled">
                                    <string>true</string>
                                </property>
                                <property name="layoutDirection">
                                    <string>0</string>
                                </property>
                                <property name="isActiveWindow">
                                    <string>true</string>
                                </property>
                                <property name="statusTip"/>
                                <property name="windowIconText"/>
                                <property name="accessibleDescription"/>
                                <property name="styleSheet"/>
                                <property name="windowModality">
                                    <string>0</string>
                                </property>
                                <property name="autoFillBackground">
                                    <string>false</string>
                                </property>
                                <property name="tabletTracking">
                                    <string>false</string>
                                </property>
                                <property name="minimized">
                                    <string>false</string>
                                </property>
                                <property name="maximized">
                                    <string>false</string>
                                </property>
                                <property name="toolTip"/>
                            </properties>
                            <identifyingProperties>
                                <property name="accessibleName"/>
                                <property name="objectName"/>
                            </identifyingProperties>
                        </element>
                    </state>
                </execution>
            </ui>
            <check type="identifying"/>
            <check type="screenshot">
                <PropertyVerification element-id="1790cb02503"/>
            </check>
        </visual>
    </Verification>
</VerificationPoint>
