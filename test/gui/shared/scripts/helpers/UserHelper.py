from base64 import b64encode
from typing import NamedTuple


class User(NamedTuple):
    username: str
    password: str
    displayname: str
    email: str


test_users = {
    "admin": User(
        username="admin",
        password="admin",
        displayname="adminUsername",
        email="<EMAIL>",
    ),
    "Alice": User(
        username="<PERSON>",
        password="1234",
        displayname="<PERSON>",
        email="<EMAIL>",
    ),
    "<PERSON>": User(
        username="<PERSON>",
        password="AaBb2Cc3Dd4",
        displayname="<PERSON>",
        email="<EMAIL>",
    ),
    "<PERSON>": User(
        username="<PERSON>",
        password="1234",
        displayname="<PERSON>",
        email="<EMAIL>",
    ),
    "<PERSON>": User(
        username="<PERSON>",
        password="1234",
        displayname="<PERSON>",
        email="<EMAIL>",
    ),
}


def get_default_password():
    return "1234"


def basic_auth_header(user=None, password=None):
    if not user and not password:
        user = "admin"
        password = "admin"
    elif not user == "public" and not password:
        password = get_password_for_user(user)

    token = b64encode((f"{user}:{password}").encode()).decode()
    return {"Authorization": "Basic " + token}


def get_user_info(username, attribute):
    if username in test_users:
        return getattr(test_users[username], attribute)
    if attribute == "password":
        return get_default_password()
    raise ValueError(f"Invalid user attribute: {attribute}")


def get_displayname_for_user(username):
    return get_user_info(username, "displayname")


def get_password_for_user(username):
    return get_user_info(username, "password")
