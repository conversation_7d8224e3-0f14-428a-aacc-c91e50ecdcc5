Feature:  Logout users
  As a user
  I want to be able to login and logout of my account
  So that I can protect my work and identity and be assured of privacy

    Background:
        Given user "<PERSON>" has been created in the server with default attributes


    Scenario: logging out
        Given user "<PERSON>" has set up a client with default settings
        When the user "<PERSON>" logs out using the client-UI
        Then user "<PERSON>" should be signed out


    Scenario: login after logging out
        Given user "<PERSON>" has set up a client with default settings
        And user "<PERSON>" has logged out from the client-UI
        When user "<PERSON>" logs in using the client-UI
        Then user "<PERSON>" should be connected to the server
        When the user quits the client
        And the user starts the client
        Then user "<PERSON>" should be connected to the server
