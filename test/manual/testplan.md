## Preparation

- create a release or test plan issue in GitHub
- copy each test section of this test plan into a separate comment in the issue
- add links to every section in the main comment
  ```
  * [ ] 1. Install, Setup and Configuration @tester1
    -> https://github.com/opencloud-eu/desktop/issues/xxxx#issuecomment-xxxxxxxxx
  * [ ] 2. Folders
    -> ...
  * [ ] 3. Files
    -> ...
  * [ ] 4. Move files and folders
    -> ...
  * [ ] 5. Edit Files
    -> ...
  * [ ] 6. Delete Files and Folders
    -> ...
  * [ ] 7. Sync process
    -> ...
  * [ ] 8. Spaces and Sharing
    -> ...
  * [ ] 9. Pause and resume sync
    -> ...
  * [ ] 10. Advanced configuration
    -> ...
  * [ ] 11. Selective sync
    -> ...
  * [ ] 12. Overlay icons
    -> ...
  * [ ] 13. Context menu
    -> ...
  ```

- If not stated otherwise each test should be run on every platform win/linux/mac. Record the result as per platform.
- "Enable logging to temporary folder" and "Log Http traffic" (tab 'Settings', button 'Log Settings') to have log-files available if needed to report an issue.
- Make sure all automated UI tests run successfully in CI and run them locally on Windows.
- all tests that are automated are marked with a :robot: in the 'Result' column (for the corresponding platform) and the name of the test in the 'Related Comment' column
- If multiple people are working on the tests at the same time, mark the tests everyone is working on with the GitHub name of the tester.
- Add the test result to the 'Result' column. For success: :heavy_check_mark:, failure: :x: (link the reported #issue to 'Related Comment')

## Testplan

### 1. Install, Setup and Configuration

Try everything here once with Keycloak and once with Lico (the single binary builtin IdP).
To easily run a Keycloak setup, use the [deployment examples](https://github.com/opencloud-eu/opencloud/tree/main/deployments/examples/opencloud_full).

| ID | Test Case                                                     | Steps to reproduce                                                                                                                                                                                                                                                                                                                                                                                                                   | Expected Result                                                                                  | Result                                                             | Related Comment (Squish-test) |
|----|---------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Update Installation                                           | 1. Install a previous version<br>2. Update to the new version                                                                                                                                                                                                                                                                                                                                                                        |                                                                                                  | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 2  | Install the new version                                       | Delete any previous version<br><br> **Windows:** <br> - Install the desktop client using .exe installer.<br><br> **Mac:** <br> - Install the desktop client using .pkg installer <br><br> **Ubuntu or Debian with GNOME desktop:** <br> - Run the client as AppImage. <br><br> **Fedora with GNOME desktop:** <br> - Run the client as AppImage. <br><br> **All platforms:** <br> - Check the version from the Settings tab -> About | installed version is correct                                                                     | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 3  | Verify that you can enter a server address (self signed cert) | 1. Launch desktop client<br>2. Enter a server address<br>3. Click on Next<br>4. If it is the first time you should accept the certificate                                                                                                                                                                                                                                                                                            |                                                                                                  | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_addAccount                |
| 4  | Valid Login                                                   | 1. Log in with the correct username and password                                                                                                                                                                                                                                                                                                                                                                                     | Login successful                                                                                 | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_addAccount                |
| 5  | Invalid Login                                                 | 1. Try to log in with wrong username or password                                                                                                                                                                                                                                                                                                                                                                                     | Error message `Login failed: username and/or password incorrect` is shown                        | :construction: Win<br>:construction: macOS<br>:construction: Linux | tst_addAccount                |
| 6  | Skip sync folder                                              | Skip sync folder configuration during setup                                                                                                                                                                                                                                                                                                                                                                                          | Setup is completed, no folder is synced, but can be added later                                  | :robot: Win<br>:construction: macOS<br>:robot: Linux               |
| 7  | Configure sync folder                                         | Configure sync folder configuration during setup                                                                                                                                                                                                                                                                                                                                                                                     | Setup is completed, folder is synced                                                             | :construction: Win<br>:construction: macOS<br>:construction: Linux |
| 8  | Remove an account                                             | 1. Add an account to desktop client<br>2. Upload files and folders via WebUI<br>3. Wait for sync to complete<br>4. Remove the account                                                                                                                                                                                                                                                                                                | The account is removed from the Desktop client but the files and folder exist on the file system | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 9  | re-add an account                                             | 1. Add an account to desktop client<br>2. Upload files and folders via WebUI<br>3. Wait for sync to complete<br>4. Remove the account<br>5. Add the same account again                                                                                                                                                                                                                                                               | The account is added, all files and folder are synced                                            | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
### 2. Folders

- 'Go to local sync folder and create a folder' means: "Open folder" on client dot ... menu, create a new folder in file browser
- 'Verify on the server' means:  the folder is sent from client to server / check either with web browser or another client

| ID | Test Case                                                                     | Steps to reproduce                                                                                                                                                                                                                                                                                                                                                                                       | Expected Result                                                                                                                                                                                 | Result                                                             | Related Comment (Squish-test) |
|----|-------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | create a folder                                                               | 1. Add an account to desktop client<br>2. Open local sync folder<br>3. Create a folder<br>4. Wait for sync to complete                                                                                                                                                                                                                                                                                   | The folder is available on the server                                                                                                                                                           | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 2  | folder with long name                                                         | 1. Create a folder with a long name (~100 characters)<br>2. Wait for sync to complete                                                                                                                                                                                                                                                                                                                    | The folder is available on the server                                                                                                                                                           | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 3  | folder with special characters                                                | 1. Create a folder with special characters and emojis (e.g. `$%ñ&💥🫨❤️‍🔥`) in the local sync folder<br>2. Wait for sync to complete                                                                                                                                                                                                                                                                        | The folder is available on the server                                                                                                                                                           | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 4  | folder with many subfolders                                                   | 1. Inside the sync root, create a folder with 5 empty subfolders and 5 subfolders containing files<br>2. Wait for sync to complete                                                                                                                                                                                                                                                                       | All 10 subfolders are available on the server                                                                                                                                                   | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 5  | sync many folders at once                                                     | 1. Create 400 folders outside the sync root<br>2. Move those folders into the sync root<br>3. Wait for sync to complete                                                                                                                                                                                                                                                                                  | All 400 folders are available on the server                                                                                                                                                     | :construction: Win<br>:construction: macOS<br>:construction: Linux | tst_syncing                   |
| 6  | copy of a folder                                                              | 1. Open local sync folder<br>2. Create a folder with some files in it<br>3. Copy and paste that folder<br>4. Wait for sync to complete<br>5. Check the folders and the content of file inside it                                                                                                                                                                                                         | Both original and copied folders are available and contains the file on the server                                                                                                              | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 7  | subfolder with long name                                                      | 1. Open local sync folder<br>2. Create a folder<br>3. Create a subfolder with long name (~220 characters)<br>4. Wait for sync to complete                                                                                                                                                                                                                                                                | Folder and subfolder are available on the server                                                                                                                                                | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 8  | pre-existing folders                                                          | 1. Quit the desktop client<br>2. Goto the local sync folder<br>3. Create folders at several levels<br>4. Open the desktop client<br>5. Wait for sync to complete                                                                                                                                                                                                                                         | All folders are available on the server                                                                                                                                                         | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 9  | .zip/.rar files with elaborate internal folder structures                     | 1. Create a zip file with many internal folders and files<br>2. Copy that zip file into the sync root<br>3. Unzip that zip file inside the sync root                                                                                                                                                                                                                                                     | All extracted files and folders are available on the server                                                                                                                                     | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 10 | Files that cause an error with the API should try 3 times, and then blacklist | 1. Try to sync a folder that has more than 65 characters<br>2. sync it with some contents<br>3. rename the folder to a name with less than 65 characters                                                                                                                                                                                                                                                 | The sync should be tried 3 times and then the folder should be blacklisted, after the rename the sync should succeed.                                                                           | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 11 | Invalid system names                                                          | 1. On the server, create folders `CON`, `COM1` and `test%` and files `PRN` and `foo%`<br>2. Add that account to the desktop client                                                                                                                                                                                                                                                                       | - MacOS client syncs down `CON`, `COM1` and `PRN` but not `test%` and `foo%`<br>- Windows client syncs down `test%` and `foo%` but not `CON`, `COM1` and `PRN`<br>- Linux client syncs down all | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 12 | Long path on Windows                                                          | 1. Make sure LongPathsEnabled in Windows registry is 'off' (see https://docs.microsoft.com/en-us/windows/win32/fileio/maximum-file-path-limitation?tabs=cmd) <br>2. On the server, create a file (~30 chars) inside 6 subfolders (each ~50 chars) to get a path length > 260 chars (> MAX_PATH)<br>3. Start desktop client<br>4. Open local sync folder and enter the subfolders<br>5. Create a new file | 1. All subfolders and the file are available in the local sync folder<br>2. New file is synced to the server                                                                                    | :construction: Win                                                 |                               |

### 3. Files

Note: "Via Web" means check files on server in the web browser

| ID | Test Case                                                    | Steps to reproduce                                                                                                                                                                                                                                                                           | Expected Result                                                                                                         | Result                                                             | Related Comment (Squish-test) |
|----|--------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | create a file                                                | 1. Add an account to desktop client<br>2. Open local sync folder<br>3. Create a file                                                                                                                                                                                                         | - observe a BLUE progress icon followed by a GREEN icon in the file explorer<br>- the file is available on the server   | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 2  | Add various types of files                                   | 1. Open local sync folder<br>2. Create/upload various types of files `Microsoft Word`, `Microsoft Excel`, `Microsoft Powerpoint`, `JPG`, `PNG`, `JPEG`, `PDF`, `MP3`, `MP4`, etc                                                                                                             | - the Sync files tab shows all the files as synced<br>- all files are available on the server                           | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 3  | File with long name can be synced                            | 1. Open local sync folder<br>2. Create a file with long name: `thisIsAVeryLongFileNameToCheckThatItWorks-thisIsAVeryLongFileNameToCheckThatItWorks-thisIsAVeryLongFileNameToCheckThatItWorks-thisIsAVeryLongFileNameToCheckThatItWorks-thisIsAVeryLongFileNameToCheckThatItWorks-thisIs.txt` | Sync is successful                                                                                                      | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 4  | File with a name having 233 characters or more               | 1. Open local sync folder<br>2. Create a file having >=233 characters<br>3. Observe the sync process                                                                                                                                                                                         | The "Not synced" tab shows an error, the file is blacklisted                                                            | :construction: Win<br>:construction: macOS<br>:construction: Linux | tst_syncing                   |
| 5  | copy/drag&drops multiple files at a time                     | 1. Copy some files to the local sync folder<br>2. Drag&Drop some files to the local sync folder                                                                                                                                                                                              | - user can see the completed GREEN icon overlay on all types of files<br>- the Sync files tab shows all files as synced | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 6  | Sync files (from server and desktop client) at the same time | 1. Add a (big) file inside of the local sync folder, and in the same time, upload another file through the webUI in the same folder (make sure that both files are uploaded at the same time)                                                                                                | Both files are synced correctly. Check via Web UI and the Desktop Client                                                | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 7  | Same name files but with different extensions                | 1. Open local sync folder<br>2. Create two files with same name but different extensions                                                                                                                                                                                                     | Both files sync correctly                                                                                               | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 8  | File with spaces in the name                                 | 1. Open local sync folder<br>2. Create, copy or move a file (having space in its name) in the local sync folder                                                                                                                                                                              | File syncs correctly                                                                                                    | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 9  | Create and delete a file with special characters in the name | 1. Open local sync folder<br>2. Create/upload a file with special characters in its name (e.g. **~`!@#$^&()-_=+{[}];',$%ñ&💥🫨❤️‍🔥**)<br>3. Wait for sync to complete<br>4. Delete the file from the local sync folder                                                                      | Look via the Web and make sure that the file got deleted                                                                | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 10 | Upload a 50MB file                                           | 1. Create a folder inside the local sync folder<br>2. Copy a large file (50MB) in this folder                                                                                                                                                                                                | The file is synced to the server                                                                                        | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 11 | Upload a 3GB file                                            | 1. On the server, upload a large 3GB file<br>2. Add that account to desktop client                                                                                                                                                                                                           | File is synced to the desktop client                                                                                    | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 12 | Upload multiple files (with a sum of 1000MB)                 | 1. Open local sync folder<br>2. Upload a folder containing 1000 files (1MB each)                                                                                                                                                                                                             | All files are synced to the server                                                                                      | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 13 | Upload 500MB + 500MB files                                   | 1. Open temp folder<br>2. Create two folders containing 500 files each(1MB per file)<br>3. Move those folders from temp folder to sync folder<br>4. Wait for folders to be synced                                                                                                            | All folders and files are synced to the server                                                                          | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 14 | Upload a 1024MB file                                         | 1. Open local sync folder<br>2. Upload a 1GB file                                                                                                                                                                                                                                            | The file is synced to the server                                                                                        | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 15 | quota limit                                                  | 1. Open local sync folder<br>2. Upload the necessary large files to fill up the quota                                                                                                                                                                                                        | Warning: "The available space of your workspace is running out, please delete some files to free space"                 | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |

### 4. Move files and folders

| ID | Test Case                                             | Steps to reproduce                                                                                                                                           | Expected Result                                                      | Result                                                             | Related Comment (Squish-test) |
|----|-------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Move from sub-levels to root                          | 1. Move couple of files and folders from different sub-levels to the sync root<br>2. Wait for sync to complete<br>3. Check the contents of files and folders | The contents are correct (on the server)                             | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 2  | Move a folder and file down to sub-folder             | 1. Move a folder and file from sync root to a 5 level deep sub-folder<br>2. Wait for sync to complete<br>3. Check the content of file and folder exist       | The content of the file is correct and folder exists (on the server) | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 3  | Move a folder and file up from sub-folder to the root | 1. Move a folder and file from a 5 level deep folder to the sync root<br>2. Wait for sync to complete<br>3. Check the content of file and folder exist       | The content of the file is correct and folder exists (on the server) | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 4  | Move files from one folder to another                 | 1. Move some files from `Folder1` to `Folder2`<br>2. Wait for sync to complete<br>3. Check both folders contents                                             | Both folders have the correct content (on the server)                | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |

### 5. Edit Files

| ID | Test Case                               | Steps to reproduce                                                                                                                                                                                                                                                                               | Expected Result                                                         | Result                                                             | Related Comment (Squish-test) |
|----|-----------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Edit a .txt file                        | 1. Create a .txt file in the local sync folder<br>2. Add some text<br>3. Wait for it to sync<br>4. Modify the .txt file and add more content<br>5. Wait for it to sync                                                                                                                           | The file at the server had the same content after the sync is completed | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_editFiles                 |
| 2  | Edit a .doc file                        | 1. Create a .doc file in the local sync folder<br>2. Add some text<br>3. Wait for it to sync<br>4. Modify the .doc file and add more content<br>5. Wait for it to sync<br>6. Modify the .doc file and add more content<br>7. Wait for it to sync                                                 | The file at the server had the same content after the sync is completed | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 3  | Edit a .xls file                        | 1. Create a .xls file in the local sync folder<br>2. Add some text<br>3. Wait for it to sync<br>4. Modify the .xls file and add more content<br>5. Wait for it to sync<br>6. Modify the .xls file and add more content<br>7. Wait for it to sync                                                 | The file at the server had the same content after the sync is completed | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | Replace a .pdf file                     | 1. Create a .pdf file in the local sync folder<br>2. Replace it with a same name pdf file having different content<br>3. Wait for it to sync<br>4. Modify the .pdf file and add more content<br>5. Wait for it to sync<br>6. Modify the .pdf file and add more content<br>7. Wait for it to sync | The file at the server had the same content after the sync is completed | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 5  | Edit a file while the folder is renamed | 1. Add an account to desktop client that has a folder containing a file in it<br>2. From the file explorer, open that file and edit it<br>3. From the web, rename the folder<br>4. Sync with the oc-worker<br>5. Do not refresh the browser and download the file edited                         | The file on the server has the same content                             | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 6  | Rename file and folder at the same time | 1. Add an account to desktop client that has a folder containing a file in it<br>2. From the web, rename that folder and at the same time from the file explorer, rename a file contained in that folder                                                                                         | File and folder should be renamed both in the server and locally        | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |

### 6. Delete Files and Folders

| ID | Test Case                      | Steps to reproduce                                                                                                                                      | Expected Result                     | Result                                                             | Related Comment (Squish-test)                                        |
|----|--------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|--------------------------------------------------------------------|----------------------------------------------------------------------|
| 1  | Delete a file                  | 1. On the server, create a file<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete that file                    | The file is deleted on the server   | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_deletFilesFolders                                                |
| 2  | Delete a file with long name   | 1. On the server, create a file with long name<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete that file     | The file is deleted on the server   | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_deletFilesFolders (Maximum length of filename is 228 chraracter) |
| 3  | Delete a folder                | 1. On the server, create a folder<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete that folder                | The folder is deleted on the server | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_deletFilesFolders                                                |
| 4  | Delete a folder with long name | 1. On the server, create a folder with long name<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete that folder | The folder is deleted on the server | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_deletFilesFolders                                                |
| 5  | Delete multiple files          | 1. On the server, create some files<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete some files               | The files get deleted on the server | :construction: Win<br>:construction: macOS<br>:construction: Linux |                                                                      |
| 6  | Delete a large file (2048MB)   | 1. On the server, create a large file (2048MB)<br>2. Add that account to the desktop client<br>3. Open the local sync folder<br>4. Delete that file     | The file gets deleted on the server | :construction: Win<br>:construction: macOS<br>:construction: Linux |                                                                      |

### 7. Sync process

| ID | Test Case                                  | Steps to reproduce                                                                                                                                                                                                               | Expected Result                                                                                | Result                                                             | Related Comment (Squish-test) |
|----|--------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Move a file while in sync                  | 1. Move couple of files (`File1` and `File2`) with different contents to the root sync folder<br>2. Let them sync<br>3. Move other files to the root sync folder and while in sync,  delete `File1` and rename `File2` to`File1` | The content of file is correct                                                                 | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 2  | Edit a file while in sync                  | 1. Create a .txt file with some content<br>2. Let it sync<br>3. Rename the file and while in sync, edit the content of the .txt file                                                                                             | The content of file is correct                                                                 | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 3  | Delete folders while in sync               | 1. Create folders tree folders+subfolders (e.g folder1, folder2, folder3 and some .txt files in this folder)<br>2. Delete some folders while in sync                                                                             | Folders get deleted on the server                                                              | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | Delete folders while sync with two clients | 1. Create a tree of folders+subfolders (e.g folder1, folder2, folder3 and some .txt files in this folder)<br>2. Delete some folders and while in sync, sync with another client at the same time                                 | On the server, make sure that the folders got deleted and the remaining folders sync correctly | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |

### 8. Spaces and Sharing

| ID | Test Case                                        | Steps to reproduce                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Expected Result                                                                                                           | Result                                                             | Related Comment (Squish-test) |
|----|--------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Viewer can view files in Space                   | 1. On the server, create a space, upload a file in it and add a user with Viewer role<br>2. As a space member (Viewer), add that space to the desktop client<br>3. Open the local sync folder and open that file                                                                                                                                                                                                                                                              | The file can be opened                                                                                                    | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 2  | Viewer cannot edit files in Space                | 1. On the server, create a space, upload a file in it and add a user with Viewer role<br>2. As a space member (Viewer), add that space to the desktop client<br>3. Open the local sync folder<br>4. Edit that file and save it                                                                                                                                                                                                                                                | Changes are not synced. Locally a conflicted copy is created                                                              | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 3  | Editor can rename files in Space                 | 1. On the server, create a space, upload a file in it and add a user with Editor role<br>2. As a space member (Editor), add that space to the desktop client<br>3. Open the local sync folder<br>4. Rename that file                                                                                                                                                                                                                                                          | The files is renamed and synced                                                                                           | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | Manager can add new folders in Space             | 1. On the server, create a space, upload a file in it and add a user with Manager role<br>2. As a space member (Manager), add that space to the desktop client<br>3. Open the local sync folder<br>4. Create a new folder                                                                                                                                                                                                                                                     | New folder is synced                                                                                                      | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 4  | Remove a project space sync connection           | 1. On the server, create a space, upload a file in it and add a user with Manager role<br>2. As a space member (Manager), add that space to the desktop client<br>3. Wait till all files and folder are synced<br>4. Remove the space from the desktop client                                                                                                                                                                                                                 | Space is deleted from the list, all files and folders still exist in the local sync folder                                | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | sync a received shared folder `can view(secure)` | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can view(secure)` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. create folders locally in the received share<br>6. copy files into the received local folder                                                                                                                                                  | Folder is shown, but no files are synced in ether direction, error message shown by the client                            | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | sync a received shared folder `can view`         | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can view` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. create folders locally in the received share<br>6. copy files into the received local folder                                                                                                                                                          | Folder and files are downloaded, but no folder/files are uploaded to the server, error message shown by the client        | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 5  | sync a received shared folder `can upload`       | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can upload` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. create folders locally in the received share<br>6. copy files into the received local folder<br>7. edit a local file                                                                                                                                | Folder and files are synced in both directions, but editing results in a new conflict file and the change is not uploaded | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 6  | sync a received shared folder `can edit`         | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can edit` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. create folders locally in the received share<br>6. copy files into the received local folder<br>7. edit a local file                                                                                                                                  | Folder and files are synced in both directions                                                                            | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 7  | remove a share                                   | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can edit` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. wait till the folder is synced locally<br>6. Delete the share on the server                                                                                                                                                                           | Folder and files disappear from the local sync folder                                                                     | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 8  | remove a share while offline                     | 1. On the server, create a folder<br>2. upload some files into it<br>3. Share the folder with an other user with `can edit` permissions<br>4. As the share receiver, add the "Shares" space to the desktop client<br>5. wait till the folder is synced locally<br>6. Disconnect from the server e.g. by disconnecting from the internet<br>7. Add a new file to the shared folder locally<br>8. Delete the share on the server<br>9. reestablish the connection to the server | No files or folder are deleted locally, client shows an error message                                                     | :construction: Win<br>:construction: macOS<br>:construction: Linux |

### 9. Pause and resume sync

| ID | Test Case                                                        | Steps to reproduce                                                                                                                                                                                                                                                                                                                                                  | Expected Result                                                            | Result                                                             | Related Comment (Squish-test) |
|----|------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Sync after reestablishing the connection                         | 1. Add an account to the desktop client<br>2. Disconnect from the server e.g. by disconnecting from the internet<br>3. Copy several files and folders into the sync folder<br>4. reestablish the connection to the server<br>5. Wait for sync                                                                                                                       | The files and folder are synced to the server                              | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 3  | Merge folder content after reestablishing the connection         | 1. Add an account to the desktop client<br>2. Disconnect from the server e.g. by disconnecting from the internet<br>3. Upload a folder with some files to the server e.g. by the webUI or an other client<br>4. In the local sync folder, create a folder with the same name but different files<br>5. reestablish the connection to the server<br>6. Wait for sync | All files and folder are synced to the server and to the local sync folder | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | Deletion after reestablishing the connection                     | 1. Add an account to the desktop client<br>2. Disconnect from the server e.g. by disconnecting from the internet<br>3. Delete a folder from the local sync folder<br>4. Connect the Internet<br>5. Wait for sync                                                                                                                                                    | The folder is deleted from the server                                      | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 5  | manually pause/resume a sync                                     | 1. Add an account to the desktop client<br>2. Upload big files to the server into a project space & personal space via WebUI<br>3. Copy other big files into the local sync folder of the project space & personal space<br>4. Pause the sync while files are uploaded/downloaded<br>5. Resume the sync.                                                            | All files are folders are synced correctly in both directions              | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 6  | Force a sync while an Upload to a different space is running     | 1. Add an account to the desktop client<br>2. Copy small files into the local sync folder of a project space<br>3. Copy big files into the local sync folder of the personal space<br>4. Wait till the personal space sync is finished and project space sync still runs<br>5. Force a sync of the personal space.                                                  | All files are folders are synced correctly in both directions              | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 7  | Force a sync while an Download from a different space is running | 1. Add an account to the desktop client<br>2. Upload small files into a project space using the webUI<br>3. Upload big files into a project space using the webUI<br>4. Wait till the personal space sync is finished and project space sync still runs<br>5. Force a sync of the personal space.                                                                   | All files are folders are synced correctly in both directions              | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 8  | Conflicting file                                                 | 1. Add an account to the desktop client<br>2. save a file in the local sync folder<br>3. Disconnect from the server e.g. by disconnecting from the internet<br>4. Edit the same file locally and on the server<br>4. reestablish the connection to the server<br>5. Wait for sync                                                                                   | Conflict file are created locally and on the server                        | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |

### 10. Advanced configuration

| ID | Test Case                                   | Steps to reproduce                                                                                                                                                                                                                                  | Expected Result                                                                                   | Result                                                             | Related Comment (Squish-test) |
|----|---------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Advanced configuration                      | 1. Start the desktop client and fill in the server details<br>2. Check the advanced configuration checkbox                                                                                                                                          | `Download everything` option is selected                                                          | :robot: Win<br>:construction: macOS<br>:robot: Linux               |                               |
| 2  | Configure synchronization manually          | 1. Start the desktop client and fill in the server details<br>2. Check the advanced configuration checkbox<br>3. choose `Configure synchronization manually`<br>4. Connect the account<br>5. Choose a space and a local folder                      | The space is synced into the chosen folder                                                        | :robot: Win<br>:construction: macOS<br>:robot: Linux               |
| 3  | Configure synchronization manually, a space | 1. Start the desktop client and fill in the server details<br>2. Check the advanced configuration checkbox<br>3. choose `Configure synchronization manually`<br>4. Connect the account<br>5. Choose "Cancel" in the next screen                     | - No local sync folder is created<br>- The setting window is opened and the account is registered | :construction: Win<br>:construction: macOS<br>:construction: Linux |

### 11. Selective sync

| ID | Test Case                                | Steps to reproduce                                                                                                                                                                                                                                                                                                  | Expected Result                                                                                    | Result                                                             | Related Comment (Squish-test) |
|----|------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | sync only one folder                     | 1. Upload some files and folders to the server<br>2. add an account to the desktop client with manual sync configuration<br>3. Choose the personal space to be synced<br>4. choose a local folder<br>5. Select only one folder to be synced and add the connection                                                  | Only one folder is synced                                                                          | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |
| 3  | unselected subfolders                    | 1. Upload a folder that has many subfolders to the server<br>2. Connect the desktop client and sync the personal space<br>From the `...` button for the space select "Chose what to sync" window, select the folder that has many subfolders<br>3. Extend that folder and unselect some subfolders<br>3. Click "OK" | The parent folder is synced but not the unselected subfolders                                      | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | Folder without subfolder in the list     | 1. From the `Deselect remote folders...` window, click on the `>` for a folder that does not have subfolders                                                                                                                                                                                                        | the `>` disappears                                                                                 | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 5  | sync files both ways for selected folder | 1. From the `Deselect remote folders...` window, select a folder to sync and add the connection<br>2. Upload some files via webUI into that folder<br>3. Copy some other files into the corresponding local folder<br>4. Wait for sync                                                                              | Files are synced both ways                                                                         | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 6  | sync files for unselected folder         | 1. From the `Deselect remote folders...` window, unselect a folder and add connection<br>2. From the server, upload some files in that unselected folder                                                                                                                                                            | The folder and files are not available in the sync folder<br>Previously synced folders are deleted | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 10 | sync of files in root folder             | 1. From the `Deselect remote folders...` window, unselect all the folders<br>2. Add the connection                                                                                                                                                                                                                  | files that are in the root folder are synced                                                       | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 11 | sorting of folders                       | 1. In the `Deselect remote folders...` window, sort the folders by name and size                                                                                                                                                                                                                                    | Sorting works                                                                                      | :robot: Win<br>:construction: macOS<br>:robot: Linux               | tst_syncing                   |

### 12. Overlay icons

| ID | Test Case               | Steps to reproduce                                                                                                                                           | Expected Result                                                                                                                                                                                                                                           | Result             | Related Comment (Squish-test) |
|----|-------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|-------------------------------|
| 1  | overlay icons           | 1. Launch the Desktop client <br> 2. Open the file explorer                                                                                                  | Check owncloud overlay icons are present.                                                                                                                                                                                                                 | :construction: Win |                               |
| 2  | offline state           | 1. Disconnect from the server e.g. by disconnecting from the internet<br>2. Launch the Desktop client<br>3. Check the overlay icons in the local sync folder | The overlay icons are not shown                                                                                                                                                                                                                           | :construction: Win |                               |
| 3  | paused sync             | 1. Launch the Desktop client<br>2. Check the overlay icons<br>3. Pause the sync<br>4. Check the overlay icons                                                | The overlay icons disappear                                                                                                                                                                                                                               | :construction: Win |                               |
| 4  | everything synced       | 1. Open the local sync folder<br>2. Add a folder having multiple nested files and folders<br>3. Check the overlay icons                                      | The green check is shown in all the folders/files                                                                                                                                                                                                         | :construction: Win |                               |
| 5  | sync in progress        | 1. Open the local sync folder<br>2. Add some files and folders<br>3. Check the overlay icons                                                                 | The files/folder that are waiting to sync have the blue icons                                                                                                                                                                                             | :construction: Win |                               |
| 6  | warning during sync     | 1. Open the local sync folder<br>3. Add a problematic file (path longer than 255 characters)<br>3. Check the overlay icons                                   | The files that are not synced due to a problem have the yellow warning triangle icons                                                                                                                                                                     | :construction: Win |                               |
| 7  | fatal error during sync | 1. Open the local sync folder<br>3. Create a sync error<br>3. Check the overlay icons                                                                        | The files/folders that are not synced due to a fatal problem have the red error icons                                                                                                                                                                     | :construction: Win |                               |

### 13. Context menu

| ID | Test Case           | Steps to reproduce                                                                                                                                       | Expected Result                                                                                       | Result                                                             | Related Comment (Squish-test) |
|----|---------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------|
| 1  | Sharing             | 1. Open the context menu of a synced file <br>2. Click on "Share ..."                                                                                    | A browser window opens and takes the user to the sharing page of the file                             | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 2  | Private Link        | 1. Open the context menu of a synced file <br>2. Click on "Copy private Link to clipboard"<br>3. Paste the link from the clipboard into a browser window | The browser takes the user to the file                                                                | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 3  | Show in web browser | 1. Open the context menu of a synced markdown file <br>2. Click on "Show in web browser"                                                                 | A browser window opens and takes the user to the file, opened in the markdown editor                  | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
| 4  | file versions       | 1. Open the context menu of a synced markdown file <br>2. Click on "Show file versions in web browser"                                                   | A browser window opens and takes the user to the file, with the side bar opened, showind the versions | :construction: Win<br>:construction: macOS<br>:construction: Linux |                               |
