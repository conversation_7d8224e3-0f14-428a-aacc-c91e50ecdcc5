# configuration for Black.

# NOTE: you have to use single-quoted strings in TOML for regular expressions.
# It's the equivalent of r-strings in Python.  Multiline strings are treated as
# verbose regular expressions by Black.  Use [ ] to denote a significant space
# character.

[tool.black]
line-length = 88
target-version = ['py36', 'py37', 'py38']
include = '\.pyi?$'
skip-string-normalization = true
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
/test/gui/shared/scripts/names.py
'''