#include <winresrc.h>

VS_VERSION_INFO VERSI<PERSON>IN<PERSON><PERSON>
    FILEVERSION @MIRALL_VERSION_MAJOR@,@MIRALL_VERSION_MINOR@,@MIRALL_VERSION_PATCH@,@OC_RC_VERSION_BUILD@
    PROD<PERSON><PERSON>ERSION @MIRALL_VERSION_MAJOR@,@MIRALL_VERSION_MINOR@,@MIRALL_VERSION_PATCH@,@OC_RC_VERSION_BUILD@
    FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
#ifdef _DEBUG
    FILEFLAGS VS_FF_DEBUG
#else
    FILEFLAGS 0x0L
#endif
    FILEOS VOS__WINDOWS32
    FILETYPE @OC_RC_TYPE@
    FILESUBTYPE VFT2_UNKNOWN
    BEGIN
        BLOCK "StringFileInfo"
        BEGIN
            BLOCK "040904E4"
            BEGIN
                VALUE "CompanyName", "@APPLICATION_VENDOR@\0"
                VALUE "LegalCopyright", "Copyright (C) 2025 OpenCloud GmbH\r\nCopyright (C) 2014-@MIRALL_VERSION_YEAR@ ownCloud GmbH\0"
                VALUE "FileVersion", "@MIRALL_VERSION_FULL@\0"
                VALUE "Comments", "@MIRALL_VERSION_STRING@\0"
                VALUE "FileDescription", "@APPLICATION_NAME@\0"
                VALUE "ProductName", "@APPLICATION_NAME@\0"
                VALUE "ProductVersion", "@MIRALL_VERSION_STRING@\0"
            END
        END
    BLOCK "VarFileInfo"
    BEGIN
        // English US, Unicode
        VALUE "Translation", 0x0409, 1200
    END
END
