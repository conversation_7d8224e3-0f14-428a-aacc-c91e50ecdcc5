# Copyright (C) 2016 <PERSON> <<EMAIL>>
#
# You may use this file under the terms of the 3-clause BSD license.
# See the file LICENSE from this package for details.

# This is the clang-format configuration style to be used by <PERSON>t,
# based on the rules from https://wiki.qt.io/Qt_Coding_Style and
# https://wiki.qt.io/Coding_Conventions

---
# Webkit style was loosely based on the Qt style
BasedOnStyle: WebKit

Standard: c++17
ColumnLimit: 160

# Disable reflow of qdoc comments: indentation rules are different.
# Translation comments are also excluded
CommentPragmas: "^!|^:"

# We want a space between the type and the star for pointer types
PointerBindsToType: false

# We want to break before the operators, but not before a '='
BreakBeforeBinaryOperators: NonAssignment

# Braces are usually attached, but not after functions or classes declaration
BreakBeforeBraces: Custom
BraceWrapping:
    AfterClass: true
    AfterControlStatement: false
    AfterEnum: false
    AfterFunction: true
    AfterNamespace: false
    AfterObjCDeclaration: false
    AfterStruct: true
    AfterUnion: false
    BeforeCatch: false
    BeforeElse: false
    IndentBraces: false

# The coding style does not specify the following, but this is what gives
# results closest to the existing code.
AlignAfterOpenBracket: DontAlign
AlwaysBreakTemplateDeclarations: true

# Ideally we should also allow less short function in a single line, but
# clang-format does not handle that
AllowShortFunctionsOnASingleLine: Inline

SortIncludes: true

# macros for which the opening brace stays attached
ForEachMacros:   [ foreach, Q_FOREACH, BOOST_FOREACH, forever, Q_FOREVER, QBENCHMARK, QBENCHMARK_ONCE ]

# Allow two empty lines for structuring
MaxEmptyLinesToKeep: 2
KeepEmptyLinesAtTheStartOfBlocks: false

# Properly format c++11 initializer
SpaceBeforeCpp11BracedList: false
Cpp11BracedListStyle: true